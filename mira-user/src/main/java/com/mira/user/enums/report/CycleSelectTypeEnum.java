package com.mira.user.enums.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

/**
 * @program: mira_server
 * @description:
 * @author: x<PERSON><PERSON>.dai
 * @create: 2022-11-17 14:43
 **/
@Getter
public enum CycleSelectTypeEnum {
    /**
     * 周期选择
     */
    TWO(2, "Last recent cycles"),
    THREE(3, "Last 3 cycles"),
    FOUR(4, "Last 4 cycles"),
    FIVE(5, "Last 5 cycles"),
    SIX(6, "Last 6 cycles"),
    SEVEN(7, "Last 7 cycles"),
    EIGHT(8, "Last 8 cycles"),
    NINE(9, "Last 9 cycles"),
    TEN(10, "Last 10 cycles"),
    ELEVEN(11, "Last 11 cycles"),
    TWELVE(12, "Last 12 cycles"),
    ALL(-1, "All cycles");

    private final Integer code;
    private final String description;

    CycleSelectTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static Result transfer(CycleSelectTypeEnum cycleSelectTypeEnum) {
        Result result = new Result();
        result.setType(cycleSelectTypeEnum.name());
        result.setTitle(cycleSelectTypeEnum.getDescription());
        return result;
    }

    @Data
    @ApiModel("结果")
    public static class Result {
        @ApiModelProperty(value = "type")
        private String type;
        @ApiModelProperty(value = "title")
        private String title;
    }

}
