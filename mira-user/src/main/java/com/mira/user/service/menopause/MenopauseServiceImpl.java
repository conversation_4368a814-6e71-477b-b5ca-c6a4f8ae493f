package com.mira.user.service.menopause;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.TestingProductDayDTO;
import com.mira.api.bluetooth.dto.menopause.MenopauseResultDTO;
import com.mira.api.bluetooth.dto.wand.TestingWandDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.provider.IMenopauseProvider;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.bluetooth.util.TestRemindUtil;
import com.mira.api.message.provider.IMessageProvider;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.thirdparty.consts.KlaviyoPropertyConst;
import com.mira.api.user.enums.NoPeriodGoalEnum;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.api.user.enums.UserMenopauseStageEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.*;
import com.mira.user.async.KlaviyoProducer;
import com.mira.user.controller.vo.menopause.*;
import com.mira.user.handler.noperiod.NoPeriodHandler;
import com.mira.user.service.manager.CacheManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-10-10
 **/
@Slf4j
@Service
public class MenopauseServiceImpl implements IMenopauseService {
    @Resource
    private IMenopauseProvider menopauseProvider;
    @Resource
    private ISsoProvider ssoProvider;
    @Resource
    private CacheManager cacheManager;

    @Resource
    private MenopauseComponent menopauseComponent;
    @Resource
    private IMessageProvider messageProvider;
    @Resource
    private KlaviyoProducer klaviyoProducer;


    @Override
    public Integer getAfterCompletedRemind() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        return menopauseProvider.getAfterCompletedRemind(userId).getData();
    }

    @Override
    public void setAfterCompletedRemind(Integer remind) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        menopauseProvider.setAfterCompletedRemind(userId, remind);
    }

    @Override
    public TestingPlanVO testingPlan() {
        TestingPlanVO testingPlanVO = new TestingPlanVO();

        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        Integer trackingMenopause = loginUserInfoDTO.getTrackingMenopause();
        Integer goalStatus = loginUserInfoDTO.getGoalStatus();
        if (UserGoalEnum.PREGNANCY_TRACKING.getValue().equals(goalStatus)) {
            return null;
        }

        Boolean menopauseFlag = false;
        if (Integer.valueOf(1).equals(trackingMenopause)) {
            menopauseFlag = true;
        }
        Integer age = AgeUtil.calculateAge(loginUserInfoDTO.getBirthYear(), loginUserInfoDTO.getBirthMonth(),
                loginUserInfoDTO.getBirthDay());
        if (age == null || age < 35) {
            menopauseFlag = false;
        }

        //        if (!menopauseFlag){
        //            return null;
        //        }

        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);
        String today = ZoneDateUtil.format(algorithmResultDTO.getTimeZone(), System.currentTimeMillis(),
                DatePatternConst.DATE_PATTERN);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);
        List<HormoneDTO> todayHormoneDatas = hormoneDatas
                .stream()
                .filter(hormoneData -> today.equals(LocalDateUtil.dateTime2Date(hormoneData.getTest_time())))
                .filter(hormoneDTO -> hormoneDTO.getFlag() == 1)
                .collect(Collectors.toList());

        List<TestingWandDTO> testingWands = TestRemindUtil.getTestingWandDTOs(today, cycleDataDTOS);

        //首页这里要过滤掉今天之前的日期；同时显示最多 5 条
        testingWands.removeIf(testingWandDTO -> (LocalDateUtil.minusToDay(testingWandDTO.getDate(), today) < 0));

        testingPlanVO.setTestingWands(testingWands.stream().limit(5).collect(Collectors.toList()));

        Set<Integer> wandTypeSet = todayHormoneDatas.stream()
                .map(hormoneDTO -> hormoneDTO.getTest_results().getWand_type())
                .collect(Collectors.toSet());
        List<String> todayCompleted = new ArrayList<>();
        if (!wandTypeSet.isEmpty()) {
            for (Integer wandType : wandTypeSet) {
                todayCompleted.add(WandTypeEnum.get(wandType).getString());
            }
        }
        testingPlanVO.setTodayCompleted(todayCompleted);

        Set<String> purchaseCodes = new HashSet<>();
        testingWands
                .stream()
                .map(testingWandDTO -> testingWandDTO.getProductCodes())
                .flatMap(List::stream)
                .forEach(productCode -> {
                    if (WandTypeEnum.E3G_LH.getString().equals(productCode)) {
                        productCode = "0".concat(WandTypeEnum.E3G_LH.getString());
                    } else if (WandTypeEnum.PDG.getString().equals(productCode)) {
                        productCode = "0".concat(WandTypeEnum.PDG.getString());
                    }
                    purchaseCodes.add(productCode);
                });
        testingPlanVO.setPurchaseCodes(new ArrayList<>(purchaseCodes));


        if (menopauseFlag) {
            boolean isRegular = isRegular(loginUserInfoDTO);
            //先计算两个menopause report的时间
            MenopauseResultDTO menopauseResultDTO = menopauseProvider.getMenopauseResult(userId, AlgorithmRequestTypeEnum.GET_MENOPAUSE_RESULT_testingPlan).getData();
            Integer defineStage = menopauseResultDTO.getDefineStage();
            String defineStageDate = menopauseResultDTO.getDefineStageDate();
            //testPlan的开始日和结束日改为由算法提供
            String testPlanFirstStartDate = menopauseResultDTO.getTestPlanFirstStartDate();
            String testPlanFirstEndDate = menopauseResultDTO.getTestPlanFirstEndDate();
            String testPlanSecondStartDate = menopauseResultDTO.getTestPlanSecondStartDate();
            String testPlanSecondEndDate = menopauseResultDTO.getTestPlanSecondEndDate();

            testingPlanVO.setMenopauseStage(defineStage);
            // <0.6 cycle 1 ; =0.6 cycle 1 结束; >0.6 <1 cycle 2 ; =1 cycle 2结束
            Float progressStatus = menopauseResultDTO.getProgressStatus();
            Integer reportType = menopauseComponent.getReportType(defineStage);
            if (StringUtils.isNotBlank(defineStageDate)) {
                TestingPlanVO.ReportTip reportTip = new TestingPlanVO.ReportTip();
                reportTip.setId(defineStageDate);
                reportTip.setType(reportType);
                testingPlanVO.setReportTip(reportTip);
            }

            List<MenopauseReportVO> menopauseReports = menopauseComponent.buildMenopauseReports(reportType, progressStatus, defineStageDate,
                    testPlanFirstEndDate, testPlanSecondEndDate, isRegular);
            testingPlanVO.setMenopauseReports(menopauseReports);
        }

        return testingPlanVO;
    }


    @Override
    public TestingPlanDetailVO testingPlanDetail() {
        TestingPlanDetailVO testingPlanDetailVO = new TestingPlanDetailVO();
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        Integer trackingMenopause = loginUserInfoDTO.getTrackingMenopause();
        testingPlanDetailVO.setTrackingMenopause(trackingMenopause);

        Integer goalStatus = loginUserInfoDTO.getGoalStatus();

        boolean isRegular = isRegular(loginUserInfoDTO);

        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);
        String today = ZoneDateUtil.format(algorithmResultDTO.getTimeZone(), System.currentTimeMillis(),
                DatePatternConst.DATE_PATTERN);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);

        //日历中的测试计划和测试历史，
        // begin from the month that include today, show current and next month，
        // If next month has nothing useful info,then just display current month.
        String thisMonth = LocalDateUtil.format(today, DatePatternConst.DATE_PATTERN, DatePatternConst.MONTH_PATTERN);
        List<String> calendarMonths = getCalendarMonths(thisMonth);
        testingPlanDetailVO.setCalendarMonths(calendarMonths);

        //日历上需要展示的测试历史和推荐测试
        List<TestingWandDTO> calendarTestingWands = TestRemindUtil.getTestingWandDTOs(today, thisMonth,
                cycleDataDTOS, hormoneDatas);
        testingPlanDetailVO.setTestingWands(calendarTestingWands);

        if (trackingMenopause == null || trackingMenopause != 1) {
            // 按正常普通用户处理
            CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
            testingPlanDetailVO.setStartDate(currentCycleData.getDate_period_start());
            testingPlanDetailVO.setEndDate(LocalDateUtil.plusDay(currentCycleData.getDate_period_start(),
                    currentCycleData.getLen_cycle() - 1, DatePatternConst.DATE_PATTERN));

            //假定progressStatus =1
            Float progressStatus = 1f;
            //List<WandTestStatus>
            List<TestingPlanDetailVO.WandTestStatus> wandTestStatuses = buildCurrentCycleWandTestStatuses(cycleDataDTOS, hormoneDatas, today, progressStatus, isRegular, false);
            testingPlanDetailVO.setWandTestStatuses(wandTestStatuses);
            return testingPlanDetailVO;
        } else {
            // 按 menopause用户处理
            MenopauseResultDTO menopauseResultDTO = menopauseProvider.getMenopauseResult(userId,
                    AlgorithmRequestTypeEnum.GET_MENOPAUSE_RESULT_testingPlanDetail).getData();
            Integer defineStage = menopauseResultDTO.getDefineStage();
            String defineStageDate = menopauseResultDTO.getDefineStageDate();
            Float progressStatus = menopauseResultDTO.getProgressStatus();

            //testPlan的开始日和结束日改为由算法提供
            String testPlanFirstStartDate = menopauseResultDTO.getTestPlanFirstStartDate();
            String testPlanFirstEndDate = menopauseResultDTO.getTestPlanFirstEndDate();
            String testPlanSecondStartDate = menopauseResultDTO.getTestPlanSecondStartDate();//有概率为空
            String testPlanSecondEndDate = menopauseResultDTO.getTestPlanSecondEndDate();

            testingPlanDetailVO.setStartDate(testPlanFirstStartDate);
            testingPlanDetailVO.setEndDate(testPlanSecondEndDate);

            //------------------------ 以下为menopause用户需要返回的字段 ------------------------
            //List<MenopauseReportVO>
            Integer reportType = menopauseComponent.getReportType(defineStage);
            List<MenopauseReportVO> menopauseReports = menopauseComponent.buildMenopauseReports(reportType, progressStatus, defineStageDate,
                    testPlanFirstEndDate, testPlanSecondEndDate, isRegular);
            testingPlanDetailVO.setMenopauseReports(menopauseReports);
            //List<WandTestStatus>
            //planStage
            List<TestingPlanDetailVO.WandTestStatus> wandTestStatuses = null;
            if (progressStatus == 1f) {//改为取当前周期
                testingPlanDetailVO.setPlanStage(2);
                wandTestStatuses = buildCurrentCycleWandTestStatuses(cycleDataDTOS, hormoneDatas, today, progressStatus, isRegular, true);
            } else {
                wandTestStatuses = buildWandTestStatuses(cycleDataDTOS, hormoneDatas, today, progressStatus, isRegular,
                        testPlanFirstStartDate, testPlanFirstEndDate, testPlanSecondStartDate, testPlanSecondEndDate);
            }
            testingPlanDetailVO.setWandTestStatuses(wandTestStatuses);
        }

        return testingPlanDetailVO;
    }

    @NotNull
    private List<TestingPlanDetailVO.WandTestStatus> buildCurrentCycleWandTestStatuses(
            List<CycleDataDTO> cycleDataDTOS, List<HormoneDTO> hormoneDatas, String today,
            Float progressStatus, boolean isRegular, boolean isMenopause) {
        //计算productCode，推荐测试总数total,已测试总数completed
        CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
        String startDate = currentCycleData.getDate_period_start();
        Integer lenCycle = currentCycleData.getLen_cycle();
        String endDate = LocalDateUtil.plusDay(startDate, lenCycle, DatePatternConst.DATE_PATTERN);//不包含

        //找出在这个时间区间的所有有效测试数据
        List<HormoneDTO> intervalHormoneDTOS = hormoneDatas
                .stream()
                .filter(hormoneDTO -> hormoneDTO.getFlag() == 1)
                .filter(hormoneDTO -> LocalDateUtil.isBetweenDateAndEqual(hormoneDTO.getTest_time(), startDate, endDate))
                .collect(Collectors.toList());

        // 从testingWands中分出MAX和 FSH的测试日
        TestingProductDayDTO testingDayList = currentCycleData.getTesting_day_list();
        if (testingDayList == null) {
            return null;
        }
        List<String> plusTestingDates = testingDayList.getProduct03();
        List<String> confirmTestingDates = testingDayList.getProduct09();
        List<String> maxTestingDates = testingDayList.getProduct12();
        List<String> ovumTestingDates = testingDayList.getProduct16();

        List<TestingPlanDetailVO.WandTestStatus> wandTestStatuses = getWandTestStatuses(isRegular, progressStatus,
                intervalHormoneDTOS, ovumTestingDates, maxTestingDates, plusTestingDates, confirmTestingDates, isMenopause);
        return wandTestStatuses;
    }

    @NotNull
    private List<TestingPlanDetailVO.WandTestStatus> buildWandTestStatuses(
            List<CycleDataDTO> cycleDataDTOS, List<HormoneDTO> hormoneDatas, String today,
            Float progressStatus, boolean isRegular, String testPlanFirstStartDate, String testPlanFirstEndDate,
            String testPlanSecondStartDate, String testPlanSecondEndDate) {
        //计算productCode，推荐测试总数total,已测试总数completed

        //已测试总数completed为两个限定时间区间内的测试数据
        //推荐测试总数total为两个限定时间区间所在周期的测试推荐数据

        //找出这两个 startDate所处的 cycle,应该是不同的cycle
        CycleDataDTO firstCycleDataDTO = CycleDataUtil.getCurrentCycleData(testPlanFirstStartDate, cycleDataDTOS);
        String dateFirstCycleEnd = LocalDateUtil.plusDay(firstCycleDataDTO.getDate_period_start(), firstCycleDataDTO.getLen_cycle(), DatePatternConst.DATE_PATTERN);
        CycleDataDTO secondCycleDataDTO = null;
        String dateSecondPeriodStart;
        if (StringUtils.isNotBlank(testPlanSecondStartDate)) {
            secondCycleDataDTO = CycleDataUtil.getCurrentCycleData(testPlanSecondStartDate, cycleDataDTOS);
            dateSecondPeriodStart = secondCycleDataDTO.getDate_period_start();
            //String dateSecondCycleEnd = LocalDateUtil.plusDay(dateSecondPeriodStart, secondCycleDataDTO.getLen_cycle(), DatePatternConst.DATE_PATTERN);
        } else {
            dateSecondPeriodStart = null;
        }


        //找出在这个时间区间的所有有效测试数据
        List<HormoneDTO> intervalHormoneDTOS = new ArrayList<>();
        for (HormoneDTO hormoneDTO : hormoneDatas) {
            if (hormoneDTO.getFlag() != 1) {
                continue;
            }
            Integer wandType = hormoneDTO.getTest_results().getWand_type();
            if (WandTypeEnum.LH_E3G_PDG.getInteger().equals(wandType) ||
                    WandTypeEnum.E3G_LH.getInteger().equals(wandType) ||
                    WandTypeEnum.PDG.getInteger().equals(wandType)
            ) {
                if (LocalDateUtil.isBetweenDateAndEqual(hormoneDTO.getTest_time(), testPlanFirstStartDate, dateFirstCycleEnd) ||
                        (dateSecondPeriodStart != null && LocalDateUtil.isBetweenDateAndEqual(hormoneDTO.getTest_time(), dateSecondPeriodStart, testPlanSecondEndDate))
                ) {
                    intervalHormoneDTOS.add(hormoneDTO);
                }
            } else if (WandTypeEnum.FSH.getInteger().equals(wandType)) {
                boolean inFirstPeriod = LocalDateUtil.isBetweenDateAndEqual(hormoneDTO.getTest_time(), testPlanFirstStartDate, testPlanFirstEndDate);
                boolean inSecondPeriod = false;
                if (StringUtils.isNotEmpty(testPlanSecondStartDate) && StringUtils.isNotEmpty(testPlanSecondEndDate)) {
                    inSecondPeriod = LocalDateUtil.isBetweenDateAndEqual(hormoneDTO.getTest_time(), testPlanSecondStartDate, testPlanSecondEndDate);
                }
                if (inFirstPeriod || inSecondPeriod) {
                    intervalHormoneDTOS.add(hormoneDTO);
                }
            }
        }

        // 从testingWands中分出MAX和 FSH的测试日
        TestingProductDayDTO firstCycleTestingDayList = firstCycleDataDTO.getTesting_day_list();
        List<String> plusTestingDates = new ArrayList<>();
        List<String> confirmTestingDates = new ArrayList<>();
        List<String> maxTestingDates = new ArrayList<>();
        List<String> ovumTestingDates = new ArrayList<>();
        // 假设 firstCycleTestingDayList 不为空

        //无经期menopause这里需要这样处理
        if (StringUtils.isNotEmpty(testPlanSecondStartDate)
                && LocalDateUtil.after(dateFirstCycleEnd, testPlanSecondStartDate, DatePatternConst.DATE_PATTERN)) {
            dateFirstCycleEnd = testPlanSecondStartDate;
        }
        if (firstCycleTestingDayList != null) {
            filterDates(firstCycleTestingDayList.getProduct03(), testPlanFirstStartDate, dateFirstCycleEnd, plusTestingDates);
            filterDates(firstCycleTestingDayList.getProduct09(), testPlanFirstStartDate, dateFirstCycleEnd, confirmTestingDates);
            filterDates(firstCycleTestingDayList.getProduct12(), testPlanFirstStartDate, dateFirstCycleEnd, maxTestingDates);
            filterDates(firstCycleTestingDayList.getProduct16(), testPlanFirstStartDate, testPlanFirstEndDate, ovumTestingDates);
        }
        if (secondCycleDataDTO != null) {
            TestingProductDayDTO secondCycleTestingDayList = secondCycleDataDTO.getTesting_day_list();
            if (secondCycleTestingDayList != null) {
                filterDates(secondCycleTestingDayList.getProduct03(), dateSecondPeriodStart, testPlanSecondEndDate, plusTestingDates);
                filterDates(secondCycleTestingDayList.getProduct09(), dateSecondPeriodStart, testPlanSecondEndDate, confirmTestingDates);
                filterDates(secondCycleTestingDayList.getProduct12(), dateSecondPeriodStart, testPlanSecondEndDate, maxTestingDates);
                if (StringUtils.isNotEmpty(testPlanSecondStartDate) && StringUtils.isNotEmpty(testPlanSecondEndDate)) {
                    filterDates(secondCycleTestingDayList.getProduct16(), testPlanSecondStartDate, testPlanSecondEndDate, ovumTestingDates);
                }
            }
        }

        List<TestingPlanDetailVO.WandTestStatus> wandTestStatuses = getWandTestStatuses(isRegular, progressStatus,
                intervalHormoneDTOS, ovumTestingDates, maxTestingDates, plusTestingDates, confirmTestingDates, true);
        return wandTestStatuses;
    }

    // 辅助函数：筛选并添加满足时间区间条件的日期
    private static void filterDates(List<String> sourceDates, String startDate, String endDate, List<String> targetList) {
        for (String date : sourceDates) {
            if (LocalDateUtil.isBetweenDateAndEqual(date, startDate, endDate) && !targetList.contains(date)) {
                targetList.add(date);
            }
        }
    }


    @NotNull
    private static List<TestingPlanDetailVO.WandTestStatus> getWandTestStatuses(
            boolean isRegular, Float progressStatus, List<HormoneDTO> intervalHormoneDTOS,
            List<String> ovumTestingDates, List<String> maxTestingDates,
            List<String> plusTestingDates, List<String> confirmTestingDates, boolean isMenopause) {
        //查看所有有效的测试数据中，包含了多少条测试日已经测试的,必须在开始时间之后，结束时间之前

        // 使用辅助函数获取测试时间集合
        Set<String> ovumTestedTimes = extractTestedTimes(intervalHormoneDTOS, WandTypeEnum.FSH.getInteger());
        Set<String> maxTestedTimes = extractTestedTimes(intervalHormoneDTOS, WandTypeEnum.LH_E3G_PDG.getInteger());
        Set<String> plusTestedTimes = extractTestedTimes(intervalHormoneDTOS, WandTypeEnum.E3G_LH.getInteger());
        Set<String> confirmTestedTimes = extractTestedTimes(intervalHormoneDTOS, WandTypeEnum.PDG.getInteger());

        // 使用辅助函数计算完成的测试数
        Integer ovumCompleted = countCompletedTests(ovumTestingDates, ovumTestedTimes);
        Integer maxCompleted = countCompletedTests(maxTestingDates, maxTestedTimes);
        Integer plusCompleted = countCompletedTests(plusTestingDates, plusTestedTimes);
        Integer confirmCompleted = countCompletedTests(confirmTestingDates, confirmTestedTimes);

        List<TestingPlanDetailVO.WandTestStatus> wandTestStatuses = new ArrayList<>();
        if (isMenopause) {
            if (isRegular) {

            }
            if (!maxTestingDates.isEmpty()) {
                //推荐max
                TestingPlanDetailVO.WandTestStatus maxTestStatus = new TestingPlanDetailVO.WandTestStatus();
                maxTestStatus.setProductCode(WandTypeEnum.LH_E3G_PDG.getString());
                maxTestStatus.setTotal(maxTestingDates.size());
                maxTestStatus.setCompleted(maxCompleted);
                wandTestStatuses.add(maxTestStatus);
            } else {
                //推荐 plus+confirm
                TestingPlanDetailVO.WandTestStatus plusTestStatus = new TestingPlanDetailVO.WandTestStatus();
                plusTestStatus.setProductCode("0".concat(WandTypeEnum.E3G_LH.getString()));
                plusTestStatus.setTotal(plusTestingDates.size());
                plusTestStatus.setCompleted(plusCompleted);
                wandTestStatuses.add(plusTestStatus);

                TestingPlanDetailVO.WandTestStatus confirmTestStatus = new TestingPlanDetailVO.WandTestStatus();
                confirmTestStatus.setProductCode("0".concat(WandTypeEnum.PDG.getString()));
                confirmTestStatus.setTotal(confirmTestingDates.size());
                confirmTestStatus.setCompleted(confirmCompleted);
                wandTestStatuses.add(confirmTestStatus);
            }

            TestingPlanDetailVO.WandTestStatus ovumTestStatus = new TestingPlanDetailVO.WandTestStatus();
            ovumTestStatus.setProductCode(WandTypeEnum.FSH.getString());
            Integer fshTotal = 0;
            if (progressStatus == 1f) {
                fshTotal = ovumTestingDates.size();
            } else {
                fshTotal = isRegular ? 8 : 10;
                ovumCompleted = getOvumCompletedByProgressStatus(progressStatus, isRegular);
            }
            ovumTestStatus.setTotal(fshTotal);
            ovumTestStatus.setCompleted(ovumCompleted);
            wandTestStatuses.add(ovumTestStatus);
        } else {
            if (!plusTestingDates.isEmpty()) {
                TestingPlanDetailVO.WandTestStatus plusTestStatus = new TestingPlanDetailVO.WandTestStatus();
                plusTestStatus.setProductCode("0".concat(WandTypeEnum.E3G_LH.getString()));
                plusTestStatus.setTotal(plusTestingDates.size());
                plusTestStatus.setCompleted(plusCompleted);
                wandTestStatuses.add(plusTestStatus);
            }
            if (!confirmTestingDates.isEmpty()) {
                TestingPlanDetailVO.WandTestStatus confirmTestStatus = new TestingPlanDetailVO.WandTestStatus();
                confirmTestStatus.setProductCode("0".concat(WandTypeEnum.PDG.getString()));
                confirmTestStatus.setTotal(confirmTestingDates.size());
                confirmTestStatus.setCompleted(confirmCompleted);
                wandTestStatuses.add(confirmTestStatus);
            }
            if (!maxTestingDates.isEmpty()) {
                //推荐max
                TestingPlanDetailVO.WandTestStatus maxTestStatus = new TestingPlanDetailVO.WandTestStatus();
                maxTestStatus.setProductCode(WandTypeEnum.LH_E3G_PDG.getString());
                maxTestStatus.setTotal(maxTestingDates.size());
                maxTestStatus.setCompleted(maxCompleted);
                wandTestStatuses.add(maxTestStatus);
            }
            if (!ovumTestingDates.isEmpty()) {
                TestingPlanDetailVO.WandTestStatus ovumTestStatus = new TestingPlanDetailVO.WandTestStatus();
                ovumTestStatus.setProductCode(WandTypeEnum.FSH.getString());
                ovumTestStatus.setTotal(ovumTestingDates.size());
                ovumTestStatus.setCompleted(ovumCompleted);
                wandTestStatuses.add(ovumTestStatus);
            }
        }
        return wandTestStatuses;
    }

    // 辅助函数：从荷尔蒙数据中提取特定类型的测试时间集合
    private static Set<String> extractTestedTimes(List<HormoneDTO> hormoneDataList, Integer wandType) {
        return hormoneDataList.stream()
                .filter(hormoneDTO -> wandType.equals(hormoneDTO.getTest_results().getWand_type()))
                .map(hormoneDTO -> hormoneDTO.getTest_time().substring(0, 10))
                .collect(Collectors.toSet());
    }

    // 计算完成的测试次数的函数
    private static int countCompletedTests(List<String> testingDates, Set<String> testedDates) {
        int completed = 0;
        for (String date : testingDates) {
            if (testedDates.contains(date)) {
                completed++;
            }
        }
        return completed;
    }

    private static Integer getOvumCompletedByProgressStatus(Float progressStatus, boolean isRegular) {
        Integer ovumCompleted = 0;
        //fshCompleted可根据progressStatus值来反向推导
        if (progressStatus == null || progressStatus == 0.2f) {
            ovumCompleted = 0;
        } else if (progressStatus == 0.3f || progressStatus == 0.28f) {
            ovumCompleted = 1;
        } else if (progressStatus == 0.4f || progressStatus == 0.36f) {
            ovumCompleted = 2;
        } else if (progressStatus == 0.5f || progressStatus == 0.44f) {
            ovumCompleted = 3;
        } else if (progressStatus == 0.52f) {
            ovumCompleted = 4;
        } else if (progressStatus == 0.6f) {
            ovumCompleted = isRegular ? 4 : 5;
        } else if (progressStatus == 0.7f) {
            ovumCompleted = 5;
        } else if (progressStatus == 0.8f || progressStatus == 0.68f) {
            ovumCompleted = 6;
        } else if (progressStatus == 0.9f || progressStatus == 0.76f) {
            ovumCompleted = 7;
        } else if (progressStatus == 0.84f) {
            ovumCompleted = 8;
        } else if (progressStatus == 0.92f) {
            ovumCompleted = 9;
        }
        //这里可能出现一个极限情况，测满了 10条或者 8 条，会出现什么异常呢
        return ovumCompleted;
    }

    @NotNull
    private static List<String> getCalendarMonths(String thisMonth) {
        //Test plan的日历上需要展示的月份
        List<String> calendarMonths = new ArrayList<>();
        calendarMonths.add(thisMonth);
        // 解析当前月份字符串
        YearMonth currentMonth = YearMonth.parse(thisMonth);
        // 计算下个月份
        YearMonth nextMonth = currentMonth.plusMonths(1);
        // 创建格式化器并格式化下个月份为字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String nextMonthStr = nextMonth.format(formatter);
        calendarMonths.add(nextMonthStr);
        return calendarMonths;
    }

    private static boolean isRegular(LoginUserInfoDTO loginUserInfoDTO) {
        List<Integer> user_conditions = StringListUtil.strToIntegerList(loginUserInfoDTO.getConditions(), ",");
        //conditions中包含 1，表示irregular cycle
        boolean isRegular = true;
        if (user_conditions.contains(1)) {
            isRegular = false;
        }
        if (NoPeriodGoalEnum.get(loginUserInfoDTO.getNoPeriodFlag()) != null) {
            isRegular = false;
        }
        return isRegular;
    }

    @Override
    public TestingPlanClientVO testingPlanForClient() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        Integer trackingMenopause = loginUserInfoDTO.getTrackingMenopause();
        TestingPlanClientVO testingPlanClientVO = new TestingPlanClientVO();

        MenopauseResultDTO menopauseResultDTO = menopauseProvider.getMenopauseResult(userId,
                AlgorithmRequestTypeEnum.GET_MENOPAUSE_RESULT_testingPlanForClient).getData();
        BeanUtil.copyProperties(menopauseResultDTO, testingPlanClientVO);
        Integer defineStage = menopauseResultDTO.getDefineStage();
        String defineStageDate = menopauseResultDTO.getDefineStageDate();
        Float progressStatus = menopauseResultDTO.getProgressStatus();
        Integer backendStatus = menopauseResultDTO.getBackendStatus();

        //testPlan的开始日和结束日改为由算法提供
        String testPlanFirstStartDate = menopauseResultDTO.getTestPlanFirstStartDate();
        String testPlanFirstEndDate = menopauseResultDTO.getTestPlanFirstEndDate();
        String testPlanSecondStartDate = menopauseResultDTO.getTestPlanSecondStartDate();
        String testPlanSecondEndDate = menopauseResultDTO.getTestPlanSecondEndDate();

        if (defineStage != null) {
            switch (UserMenopauseStageEnum.get(defineStage)) {
                case Menopause:
                case Early_menopause:
                case Remature_menopause:
                    defineStage = 4;
                    break;
                case Post_menopause:
                    defineStage = 5;
                    break;
                case Stage1_menopause:
                case Stage1_early_menopause:
                case Stage1_remature_menopause:
                    defineStage = 104;
                    break;
                case Stage1_post_menopause:
                    defineStage = 105;
                    break;
                default:
                    break;
            }
        }
        testingPlanClientVO.setDefineStage(defineStage);

        Integer reportType = menopauseComponent.getReportType(defineStage);

        if (trackingMenopause == null || trackingMenopause != 1) {
            return testingPlanClientVO;
        }
        boolean isRegular = isRegular(loginUserInfoDTO);

        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);
        String today = ZoneDateUtil.format(algorithmResultDTO.getTimeZone(), System.currentTimeMillis(),
                DatePatternConst.DATE_PATTERN);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);
        List<HormoneDTO> todayHormoneDatas = hormoneDatas.stream()
                .filter(hormoneData -> today.equals(LocalDateUtil.dateTime2Date(hormoneData.getTest_time())))
                .filter(hormoneDTO -> hormoneDTO.getFlag() == 1)
                .collect(Collectors.toList());

        List<MenopauseReportVO> menopauseReports = menopauseComponent.buildMenopauseReports(reportType, progressStatus, defineStageDate,
                testPlanFirstEndDate, testPlanSecondEndDate, isRegular);
        testingPlanClientVO.setMenopauseReports(menopauseReports);

        List<TestingWandDTO> testingWands = TestRemindUtil.getTestingWandDTOs(today, cycleDataDTOS);

        //首页这里要过滤掉今天之前的日期；同时显示最多 5 条
        testingWands.removeIf(testingWandDTO -> (LocalDateUtil.minusToDay(testingWandDTO.getDate(), today) < 0));

        testingPlanClientVO.setTestingWands(testingWands.stream().limit(5).collect(Collectors.toList()));

        Set<Integer> wandTypeSet = todayHormoneDatas.stream()
                .map(hormoneDTO -> hormoneDTO.getTest_results().getWand_type())
                .collect(Collectors.toSet());
        List<String> todayCompleted = new ArrayList<>();
        if (!wandTypeSet.isEmpty()) {
            for (Integer wandType : wandTypeSet) {
                todayCompleted.add(WandTypeEnum.get(wandType).getString());
            }
        }
        testingPlanClientVO.setTodayCompleted(todayCompleted);

        CycleDataDTO firstTestCycle = null;
        CycleDataDTO secondTestCycle = null;
        DataRangeVO dataRange = null;
        //对于规则周期
        if (progressStatus == 0.2f) {
            CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
            int cd = LocalDateUtil.minusToDay(today, currentCycleData.getDate_period_start()) + 1;
            List<String> product16 = currentCycleData.getTesting_day_list().getProduct16();
            //如果当天是 CD1/2/3且当前周期存在 FSH测试日，当前周期是第一个测试cycle,否则预测周期是第一个测试cycle
            if ((cd == 1 || cd == 2 || cd == 3) && !product16.isEmpty()) {
                firstTestCycle = currentCycleData;
                String datePeriodStart = firstTestCycle.getDate_period_start();
                Integer lenCycle = firstTestCycle.getLen_cycle();
                String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle, DatePatternConst.DATE_PATTERN);

                //获取datePeriodStart 到 dateCycleEnd之间的测试数据
                dataRange = menopauseComponent.getIntervalDataRange(hormoneDatas, datePeriodStart, dateCycleEnd);
                menopauseComponent.setLhPickAndPdgRise(firstTestCycle, today, dataRange);
            } else {
                //预测周期是第一个测试cycle，这个时候没有测试数据
                //ignore
            }
        } else if (progressStatus > 0.2f && progressStatus <= 0.6f) {
            //当前周期是第一个测试cycle
            CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
            firstTestCycle = currentCycleData;
            String datePeriodStart = firstTestCycle.getDate_period_start();
            Integer lenCycle = firstTestCycle.getLen_cycle();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle, DatePatternConst.DATE_PATTERN);
            dataRange = menopauseComponent.getIntervalDataRange(hormoneDatas, datePeriodStart, dateCycleEnd);
            menopauseComponent.setLhPickAndPdgRise(firstTestCycle, today, dataRange);
        } else if (progressStatus > 0.6f && progressStatus <= 1f) {
            //第一个测试cycle已经过了,可以有两个cycle的数据
            // 当前周期处于第二个 cycle
            CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
            Integer cycleIndex = currentCycleData.getCycle_index();
            if (cycleIndex != 0) {
                secondTestCycle = currentCycleData;
                firstTestCycle = cycleDataDTOS.get(secondTestCycle.getCycle_index() - 1);

                String datePeriodStart = firstTestCycle.getDate_period_start();
                Integer lenCycle = firstTestCycle.getLen_cycle();
                String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle, DatePatternConst.DATE_PATTERN);
                dataRange = menopauseComponent.getIntervalDataRange(hormoneDatas, datePeriodStart, dateCycleEnd);
                menopauseComponent.setLhPickAndPdgRise(firstTestCycle, today, dataRange);

                String datePeriodStart2 = secondTestCycle.getDate_period_start();
                Integer lenCycle2 = secondTestCycle.getLen_cycle();
                String dateCycleEnd2 = LocalDateUtil.plusDay(datePeriodStart2, lenCycle2, DatePatternConst.DATE_PATTERN);
                DataRangeVO dataRange2 = menopauseComponent.getIntervalDataRange(hormoneDatas, datePeriodStart2, dateCycleEnd2);
                menopauseComponent.setLhPickAndPdgRise(secondTestCycle, today, dataRange2);
                rebuildFshDataRange(dataRange2, dataRange);
            } else {
                firstTestCycle = currentCycleData;
                String datePeriodStart = firstTestCycle.getDate_period_start();
                Integer lenCycle = firstTestCycle.getLen_cycle();
                String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle, DatePatternConst.DATE_PATTERN);
                dataRange = menopauseComponent.getIntervalDataRange(hormoneDatas, datePeriodStart, dateCycleEnd);
                menopauseComponent.setLhPickAndPdgRise(firstTestCycle, today, dataRange);
            }


        } else {
            // 当前周期处于第二个 cycle结束了fsh测试日 或者，当前周期已经过了第二个cycle
            // defineStageDate所在的周期是第二个cycle
            secondTestCycle = CycleDataUtil.getCurrentCycleData(defineStageDate, cycleDataDTOS);
            firstTestCycle = cycleDataDTOS.get(secondTestCycle.getCycle_index() - 1);

            String datePeriodStart = firstTestCycle.getDate_period_start();
            Integer lenCycle = firstTestCycle.getLen_cycle();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle, DatePatternConst.DATE_PATTERN);
            dataRange = menopauseComponent.getIntervalDataRange(hormoneDatas, datePeriodStart, dateCycleEnd);
            menopauseComponent.setLhPickAndPdgRise(firstTestCycle, today, dataRange);

            String datePeriodStart2 = secondTestCycle.getDate_period_start();
            Integer lenCycle2 = secondTestCycle.getLen_cycle();
            String dateCycleEnd2 = LocalDateUtil.plusDay(datePeriodStart2, lenCycle2, DatePatternConst.DATE_PATTERN);
            DataRangeVO dataRange2 = menopauseComponent.getIntervalDataRange(hormoneDatas, datePeriodStart2, dateCycleEnd2);
            menopauseComponent.setLhPickAndPdgRise(secondTestCycle, today, dataRange2);
            rebuildFshDataRange(dataRange2, dataRange);
        }

        testingPlanClientVO.setDataRange(dataRange);

        try {
            Integer rateScore = messageProvider.getRateMenopauseScore(userId).getData();
            testingPlanClientVO.setRateScore(rateScore);
        } catch (Exception e) {
            log.error("userId:{} get rateScore from messageProvider error occur", userId);
        }

        if (backendStatus != null) {
            String checkReportPage = "";
            switch (backendStatus) {
                case 1:
                    checkReportPage = "1st_cycle";
                    break;
                case 2:
                    checkReportPage = "preliminary_report";
                    break;
                case 3:
                    checkReportPage = "2nd_cycle";
                    break;
                case 4:
                    checkReportPage = "final_report";
                    break;
                default:
                    break;
            }
            klaviyoProducer.addMenopauseProperty(userId, KlaviyoPropertyConst.MENOPAUSE_CHECK_THE_REPORT_PAGE, checkReportPage);
        }

        return testingPlanClientVO;
    }

    private static void rebuildFshDataRange(DataRangeVO dataRange2, DataRangeVO dataRange) {
        List<String> fshDatas2 = dataRange2.getFshDatas();
        List<String> fshDatas = dataRange.getFshDatas();
        if (!fshDatas2.isEmpty()) {
            fshDatas.addAll(fshDatas2);
            // 找到最大值和最小值
            List<Float> floatList = fshDatas.stream()
                    .map(Float::parseFloat)
                    .collect(Collectors.toList());
            Float maxVal = Collections.max(floatList);
            Float minVal = Collections.min(floatList);

            // 将最大值和最小值转换成字符串并置于列表中
            List<String> fshDatas3 = Arrays.asList(String.valueOf(minVal), String.valueOf(maxVal));
            dataRange.setFshDatas(fshDatas3);
            dataRange2.setFshDatas(fshDatas3);
        }
    }

    @Override
    public TestingPlanDataRangeVO dataRangeForClient() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        Integer trackingMenopause = loginUserInfoDTO.getTrackingMenopause();
        if (trackingMenopause == null || trackingMenopause != 1) {
            return null;
        }

        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);
        String today = ZoneDateUtil.format(algorithmResultDTO.getTimeZone(), System.currentTimeMillis(),
                DatePatternConst.DATE_PATTERN);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);

        MenopauseResultDTO menopauseResultDTO = menopauseProvider.getMenopauseResult(userId,
                AlgorithmRequestTypeEnum.GET_MENOPAUSE_RESULT_dataRangeForClient).getData();
        String defineStageDate = menopauseResultDTO.getDefineStageDate();
        Float progressStatus = menopauseResultDTO.getProgressStatus();
        //testPlan的开始日和结束日改为由算法提供
        String testPlanFirstStartDate = menopauseResultDTO.getTestPlanFirstStartDate();
        String testPlanFirstEndDate = menopauseResultDTO.getTestPlanFirstEndDate();
        String testPlanSecondStartDate = menopauseResultDTO.getTestPlanSecondStartDate();
        String testPlanSecondEndDate = menopauseResultDTO.getTestPlanSecondEndDate();

        TestingPlanDataRangeVO testingPlanDataRangeVO = new TestingPlanDataRangeVO();
        List<DataRangeVO> dataRangeVOS = new ArrayList<>();

        CycleDataDTO firstTestCycle = null;
        CycleDataDTO secondTestCycle = null;

        //对于规则周期
        if (progressStatus == 0.2f) {
            CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
            int cd = LocalDateUtil.minusToDay(today, currentCycleData.getDate_period_start()) + 1;
            List<String> product16 = currentCycleData.getTesting_day_list().getProduct16();
            //如果当天是 CD1/2/3且当前周期存在 FSH测试日，当前周期是第一个测试cycle,否则预测周期是第一个测试cycle
            if ((cd == 1 || cd == 2 || cd == 3) && !product16.isEmpty()) {
                oneCycleDataRange(currentCycleData, hormoneDatas, today, dataRangeVOS);
            } else {
                //预测周期是第一个测试cycle，这个时候没有测试数据
                //ignore
            }
        } else if (progressStatus > 0.2f && progressStatus <= 0.6f) {
            //当前周期是第一个测试cycle
            CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
            oneCycleDataRange(currentCycleData, hormoneDatas, today, dataRangeVOS);
        } else if (progressStatus > 0.6f && progressStatus <= 1f) {
            //第一个测试cycle已经过了,可以有两个cycle的数据
            // 当前周期处于第二个 cycle
            CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
            if (currentCycleData.getCycle_index() == 0) {
                //当前周期是第一个测试cycle
                oneCycleDataRange(currentCycleData, hormoneDatas, today, dataRangeVOS);
            } else {
                secondTestCycle = currentCycleData;
                firstTestCycle = cycleDataDTOS.get(secondTestCycle.getCycle_index() - 1);
                twoCycleDataRange(firstTestCycle, secondTestCycle, hormoneDatas, today, dataRangeVOS);
            }
        } else {
            // 当前周期处于第二个 cycle结束了fsh测试日 或者，当前周期已经过了第二个cycle
            // defineStageDate所在的周期是第二个cycle
            secondTestCycle = CycleDataUtil.getCurrentCycleData(defineStageDate, cycleDataDTOS);
            firstTestCycle = cycleDataDTOS.get(secondTestCycle.getCycle_index() - 1);

            twoCycleDataRange(firstTestCycle, secondTestCycle, hormoneDatas, today, dataRangeVOS);
        }

        testingPlanDataRangeVO.setDataRanges(dataRangeVOS);

        Integer onlyTestFsh = 1;
        List<DataRangeVO> dataRanges = testingPlanDataRangeVO.getDataRanges();
        for (DataRangeVO dataRangeVO : dataRanges) {
            if (!dataRangeVO.getLhDatas().isEmpty() || dataRangeVO.getE3gDatas().isEmpty() || dataRangeVO.getPdgDatas().isEmpty()) {
                onlyTestFsh = 0;
            }
        }
        testingPlanDataRangeVO.setOnlyTestFsh(onlyTestFsh);
        return testingPlanDataRangeVO;
    }

    private void twoCycleDataRange(CycleDataDTO firstTestCycle, CycleDataDTO secondTestCycle, List<HormoneDTO> hormoneDatas, String today, List<DataRangeVO> dataRangeVOS) {
        String datePeriodStart = firstTestCycle.getDate_period_start();
        Integer lenCycle = firstTestCycle.getLen_cycle();
        String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle, DatePatternConst.DATE_PATTERN);
        DataRangeVO dataRange = menopauseComponent.getIntervalDataRange(hormoneDatas, datePeriodStart, dateCycleEnd);
        menopauseComponent.setLhPickAndPdgRise(firstTestCycle, today, dataRange);

        String datePeriodStart2 = secondTestCycle.getDate_period_start();
        Integer lenCycle2 = secondTestCycle.getLen_cycle();
        String dateCycleEnd2 = LocalDateUtil.plusDay(datePeriodStart2, lenCycle2, DatePatternConst.DATE_PATTERN);
        DataRangeVO dataRange2 = menopauseComponent.getIntervalDataRange(hormoneDatas, datePeriodStart2, dateCycleEnd2);
        menopauseComponent.setLhPickAndPdgRise(secondTestCycle, today, dataRange2);
        rebuildFshDataRange(dataRange2, dataRange);
        dataRangeVOS.add(dataRange);
        dataRangeVOS.add(dataRange2);
    }

    private void oneCycleDataRange(CycleDataDTO currentCycleData, List<HormoneDTO> hormoneDatas, String today, List<DataRangeVO> dataRangeVOS) {
        CycleDataDTO firstTestCycle = currentCycleData;
        String datePeriodStart = firstTestCycle.getDate_period_start();
        Integer lenCycle = firstTestCycle.getLen_cycle();
        String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle, DatePatternConst.DATE_PATTERN);
        DataRangeVO dataRange = menopauseComponent.getIntervalDataRange(hormoneDatas, datePeriodStart, dateCycleEnd);
        menopauseComponent.setLhPickAndPdgRise(firstTestCycle, today, dataRange);
        dataRangeVOS.add(dataRange);
    }

    @Override
    public void rateMenopause(Integer score) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        UserTypeEnum userTypeEnum = UserTypeEnum.get(loginInfo.getUserType());

        if (userTypeEnum == UserTypeEnum.APP_USER) {
            messageProvider.rateMenopause(loginInfo.getId(), score);
        }
    }

}
