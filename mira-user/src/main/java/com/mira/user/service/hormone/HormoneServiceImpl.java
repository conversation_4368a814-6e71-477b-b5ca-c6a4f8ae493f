package com.mira.user.service.hormone;

import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.TestingProductDayDTO;
import com.mira.api.bluetooth.dto.hormone.*;
import com.mira.api.bluetooth.dto.wand.TestRemindDTO;
import com.mira.api.bluetooth.dto.wand.WandTestBiomarkerDTO;
import com.mira.api.bluetooth.dto.wand.WandTestDataDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.enums.BarTipEnum;
import com.mira.api.bluetooth.provider.IAlgorithmProvider;
import com.mira.api.bluetooth.provider.IBluetoothProvider;
import com.mira.api.bluetooth.provider.IDataManualProvider;
import com.mira.api.bluetooth.util.HormoneDataUtil;
import com.mira.api.bluetooth.util.TestRemindUtil;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.dto.user.TemperatureDTO;
import com.mira.api.user.dto.user.diary.CustomLogConfigDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.TempUnitEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.request.PageDTO;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.NumberFormatUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.response.PageResult;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.controller.vo.hormone.UserDataManualVO;
import com.mira.user.dal.dao.AppDataManualDAO;
import com.mira.user.dal.entity.AppDataManualEntity;
import com.mira.api.bluetooth.dto.hormone.ManualDataDTO;
import com.mira.user.service.manager.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 荷尔蒙接口实现
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-07-31
 **/
@Service
public class HormoneServiceImpl implements IHormoneService {
    @Resource
    private AppDataManualDAO appDataManualDAO;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private IDataManualProvider dataManualProvider;
    @Resource
    private UserDiaryLogManager userDiaryLogManager;

    @Resource
    private UserCustomLogManager userCustomLogManager;

    @Resource
    private IBluetoothProvider bluetoothProvider;

    @Resource
    private ManualDataManager manualDataManager;
    @Resource
    private  AlgorithmCallManager algorithmCallManager;
    @Resource
    private  ISsoProvider ssoProvider;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void manualAddData(ManualDataDTO manualDataDTO) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        dataManualProvider.addHormoneData(manualDataDTO, userId, timeZone);

        // call algorithm
        //algorithmCallManager.editDBPeriod(ssoProvider.getUserLoginInfo(userId).getData(), AlgorithmRequestTypeEnum.ADD_HORMONE_DATA);
    }


    @Override
    public PageResult<UserDataManualVO> manualDataPage(PageDTO pageParam) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        UserDataManualPageRequestDTO userDataManualPageRequestDTO = new UserDataManualPageRequestDTO();
        userDataManualPageRequestDTO.setUserId(userId);
        userDataManualPageRequestDTO.setSize(pageParam.getSize());
        userDataManualPageRequestDTO.setCurrent(pageParam.getCurrent());
        UserManualDataResponseDTO userManualDataResponseDTO = dataManualProvider.userManualDataPage(userDataManualPageRequestDTO).getData();
        List<UserDataManualDTO> dataManualDTOS = userManualDataResponseDTO.getUserDataManualDTOS();
        List<UserDataManualVO> userDataManualVOS = new ArrayList<>();
        if (dataManualDTOS != null) {
            for (UserDataManualDTO userDataManualDTO : dataManualDTOS) {
                UserDataManualVO userDataManualVO = new UserDataManualVO();
                BeanUtils.copyProperties(userDataManualDTO, userDataManualVO);
                if (userDataManualDTO.getT1ConValue() != null) {
                    userDataManualVO.setT1ConValue(NumberFormatUtil.format(userDataManualDTO.getT1ConValue().floatValue()) + "");
                }
                if (userDataManualDTO.getT2ConValue() != null) {
                    userDataManualVO.setT2ConValue(NumberFormatUtil.format(userDataManualDTO.getT2ConValue().floatValue()) + "");
                }
                if (userDataManualDTO.getT3ConValue() != null) {
                    userDataManualVO.setT3ConValue(NumberFormatUtil.format(userDataManualDTO.getT3ConValue().floatValue()) + "");
                }
                if (userDataManualDTO.getStatus()==6){ //6为系统自动添加成功
                    userDataManualVO.setStatus(2);
                }
                userDataManualVOS.add(userDataManualVO);
            }
        }
        return new PageResult<>(userDataManualVOS, userManualDataResponseDTO.getTotal(), pageParam.getSize(),
                pageParam.getCurrent());
    }

    @Override
    public DataHistoryDTO getHistory(String dateStr) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AlgorithmResultDTO algorithmResult = cacheManager.getCacheAlgorithmResult(userId);
        return buildDataHistoryDTO(userId, dateStr, algorithmResult);
    }

    private DataHistoryDTO buildDataHistoryDTO(Long userId, String dateStr, AlgorithmResultDTO algorithmResultDTO) {
        DataHistoryDTO dataHistoryDTO = new DataHistoryDTO();
        List<TemperatureDTO> temperatureList = userDiaryLogManager.listTemperatureDTO(userId, dateStr);
        List<TemperatureDTO> filterTemperatureDTOList = temperatureList.stream()
                                                                       .filter(temperatureDTO -> temperatureDTO.getType() != 0)
                                                                       .collect(Collectors.toList());

        Integer barTip = algorithmResultDTO.getBarTip();
        if (BarTipEnum.HCG_TEST.getCode().equals(barTip)) {
            barTip = null;
        }
        dataHistoryDTO.setBarTip(barTip);

        List<CycleDataDTO> cycleDataDTOList = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDataList = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);
        List<HormoneDTO> dailyHormoneDataList =
                hormoneDataList.stream()
                               .filter(hormoneData
                                       -> dateStr.equals(LocalDateUtil.format(hormoneData.getTest_time(), DatePatternConst.DATE_TIME_PATTERN, DatePatternConst.DATE_PATTERN)))
                               .filter(hormoneDTO
                                       -> StringUtils.isBlank(hormoneDTO.getTest_results().getEcode()) || !hormoneDTO.getTest_results().getEcode().startsWith("EB")).collect(Collectors.toList());

        List<WandTestBiomarkerDTO> pendingWandTestBiomarkerDTOS = manualDataManager.getPendingWandTestBiomarkerDTOS(userId, dateStr);

        WandTestDataDTO wandDayTestDataDTO = new WandTestDataDTO();
        wandDayTestDataDTO.setDate(dateStr);
        wandDayTestDataDTO.setHormoneDTO(dailyHormoneDataList);
        List<WandTestBiomarkerDTO> wandTestBiomarkerDTOList = bluetoothProvider.getDayWandBiomarkerData(wandDayTestDataDTO, userId).getData();

        if (!pendingWandTestBiomarkerDTOS.isEmpty()) {
            wandTestBiomarkerDTOList.addAll(pendingWandTestBiomarkerDTOS);
        }

        // 温度及温度单位
        CustomLogConfigDTO customLogConfigDTO = userCustomLogManager.configInfo(userId);

        for (TemperatureDTO temperatureDTO : filterTemperatureDTOList) {
            WandTestBiomarkerDTO wandTestBiomarkerDTO = this.buildBBTBiomarkerDTO(temperatureDTO, customLogConfigDTO.getTempUnit());
            wandTestBiomarkerDTOList.add(wandTestBiomarkerDTO);
        }
        dataHistoryDTO.setWandTestDataList(wandTestBiomarkerDTOList);

        List<TestRemindDTO> testingDTOList = TestRemindUtil.getTestingProducts(dateStr, cycleDataDTOList, wandTestBiomarkerDTOList);
        dataHistoryDTO.setTestingDTOS(testingDTOList);
        return dataHistoryDTO;
    }

    private WandTestBiomarkerDTO buildBBTBiomarkerDTO(TemperatureDTO temperatureDTO, String tempUnit) {
        String tempTime = temperatureDTO.getTempTime();
        BigDecimal tempCSource = temperatureDTO.getTempC();
        BigDecimal tempFSource = temperatureDTO.getTempF();
        String modeError = temperatureDTO.getEcode();

        WandTestBiomarkerDTO wandTestBiomarkerDTO = new WandTestBiomarkerDTO("BBT", tempTime, modeError);
        BigDecimal tempC = tempCSource.setScale(2, RoundingMode.HALF_UP);
        BigDecimal tempF = tempFSource.setScale(2, RoundingMode.HALF_UP);
        String testValue;

        if (TempUnitEnum.C.getValue().equals(tempUnit)) {
            testValue = tempC.floatValue() + "℃" + " (" + tempF.floatValue() + "℉)";
        } else {
            testValue = tempF.floatValue() + "℉" + " (" + tempC.floatValue() + "℃)";
        }
        wandTestBiomarkerDTO.setTestValue(testValue);
        return wandTestBiomarkerDTO;
    }

    @Override
    public TestDateDTO getNextTestDate() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        AlgorithmResultDTO algorithmResult = cacheManager.getCacheAlgorithmResult(userId);
        List<CycleDataDTO> cycleDataDTOList = JsonUtil.toArray(algorithmResult.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDTOS = JsonUtil.toArray(algorithmResult.getHormoneData(), HormoneDTO.class);

        // 已测试时间
        Set<String> testDates = HormoneDataUtil.buildTestDates(hormoneDTOS);

        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        TestDateDTO testDateDTO = new TestDateDTO();

        for (CycleDataDTO cycleDataDTO : cycleDataDTOList) {
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            Integer lenCycle = cycleDataDTO.getLen_cycle();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle, DatePatternConst.DATE_PATTERN);
            //过滤掉已经结束的周期
            if (LocalDateUtil.minusToDay(today, dateCycleEnd) >= 0) {
                continue;
            }
            TestingProductDayDTO testingDayList = cycleDataDTO.getTesting_day_list();
            if (testingDayList == null) {
                continue;
            }
            List<String> product03 = testingDayList.getProduct03();
            List<String> product09 = testingDayList.getProduct09();
            List<String> product12 = testingDayList.getProduct12();
            List<String> product16 = testingDayList.getProduct16();

            String compareDate = today;
            //如果今天有测试，比较nextDay
            if (testDates.contains(today)) {
                compareDate = LocalDateUtil.plusDay(today, 1, DatePatternConst.DATE_PATTERN);
            }

            // 检查每组产品日期列表
            checkAndUpdateMinDate(product03, compareDate, testDateDTO, "03");
            checkAndUpdateMinDate(product09, compareDate, testDateDTO, "09");
            checkAndUpdateMinDate(product12, compareDate, testDateDTO, "12");
            checkAndUpdateMinDate(product16, compareDate, testDateDTO, "16");

            if (testDateDTO != null) {
                break;
            }
        }

        return testDateDTO;
    }

    private void checkAndUpdateMinDate(List<String> productDates, String compareDate, TestDateDTO testDateDTO,
                                       String productCode) {
        if (StringUtils.isBlank(testDateDTO.getDate())) {
            //从productDates中选一个最小的时间,和今天比较
            String minDate = productDates.stream()
                                         .filter(date -> LocalDateUtil.minusToDay(date, compareDate) >= 0)
                                         .min(Comparator.comparing(LocalDate::parse))
                                         .orElse(null);
            if (minDate != null) {
                testDateDTO.setDate(minDate);
                List<String> testingProductCodes = new ArrayList<>();
                testingProductCodes.add(productCode);
                testDateDTO.setTestingProductCodes(testingProductCodes);
            }
        } else {
            //从productDates中选一个最小的时间,和已经存在的minDate比较
            String oldMinDate = testDateDTO.getDate();
            String newMinDate = productDates.stream()
                                            .filter(date -> LocalDateUtil.minusToDay(date, oldMinDate) < 0
                                                    && LocalDateUtil.minusToDay(date, compareDate) >= 0)
                                            .min(Comparator.comparing(LocalDate::parse))
                                            .orElse(null);
            if (StringUtils.isNotBlank(newMinDate)) {
                //比较minDate与newMinDate是否一致
                if (newMinDate.equals(oldMinDate)) {
                    testDateDTO.getTestingProductCodes().add(productCode);
                } else {
                    testDateDTO.setDate(newMinDate);
                    List<String> testingProductCodes = new ArrayList<>();
                    testingProductCodes.add(productCode);
                    testDateDTO.setTestingProductCodes(testingProductCodes);
                }
            }
        }

    }

}
