package com.mira.user.service.front;

import com.mira.user.controller.vo.home.HomeV5CycleDataVO;
import com.mira.user.controller.vo.home.HomeV5DailyDataVO;
import com.mira.user.enums.home.HomeActionButtonCodeEnum;

/**
 * APP首页 V5接口
 *
 * @program: mira_server_microservices
 * @description:
 * @author: xizhao.dai
 * @create: 2023-12-13 10:30
 **/
public interface IHomeV5Service {
    /**
     * 首页周期数据
     *
     * @return HomeDataVO
     */
    HomeV5CycleDataVO homeCycleData(Integer cycleIndex);

    /**
     * 首页每日数据
     */
    HomeV5DailyDataVO homeDailyData(String date);

    void cacheActionButton(HomeActionButtonCodeEnum actionButtonCodeEnum);
}