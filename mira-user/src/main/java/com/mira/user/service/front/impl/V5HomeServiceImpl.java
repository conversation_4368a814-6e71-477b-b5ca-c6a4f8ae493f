

package com.mira.user.service.front.impl;

import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.PregnantRiskDTO;
import com.mira.api.bluetooth.dto.wand.*;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.enums.OvulationTypeEnum;
import com.mira.api.bluetooth.provider.IBluetoothProvider;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.bluetooth.util.TestRemindUtil;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.dto.user.TemperatureDTO;
import com.mira.api.user.dto.user.diary.CustomLogConfigDTO;
import com.mira.api.user.enums.NoPeriodGoalEnum;
import com.mira.api.user.util.UserGoalUtil;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.TempUnitEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.SpringContextHolder;
import com.mira.core.util.ZoneDateUtil;
import com.mira.user.controller.vo.chart.PregnantRiskVO;
import com.mira.user.controller.vo.home.HomeV5CycleDataVO;
import com.mira.user.controller.vo.home.HomeV5DailyDataVO;
import com.mira.user.controller.vo.menopause.TestingPlanVO;
import com.mira.user.dal.dao.AppUserBindLogDAO;
import com.mira.user.dal.entity.AppUserBindLogEntity;
import com.mira.user.dto.home.HomeShowWordDTO;
import com.mira.user.enums.home.HomeActionButtonCodeEnum;
import com.mira.user.handler.event.EnterHomePageEvent;
import com.mira.user.handler.noperiod.NoPeriodHandler;
import com.mira.user.service.front.IHomeV5Service;
import com.mira.user.service.manager.*;
import com.mira.user.service.menopause.IMenopauseService;
import com.mira.user.service.util.CycleShowWordUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * APP首页V5接口实现
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-12-13
 **/
@Slf4j
@Service
public class V5HomeServiceImpl implements IHomeV5Service {
    @Resource
    private AppUserBindLogDAO appUserBindLogDAO;
    @Resource
    private ISsoProvider ssoProvider;
    @Resource
    private UserDiaryLogManager userDiaryLogManager;
    @Resource
    private UserCustomLogManager userCustomLogManager;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private HomeActionButtonManager homeActionButtonManager;
    @Resource
    private ManualDataManager manualDataManager;
    @Resource
    private IBluetoothProvider bluetoothProvider;
    @Resource
    private IMenopauseService menopauseService;

    @Override
    public HomeV5CycleDataVO homeCycleData(Integer cycleIndex) {
        HomeV5CycleDataVO homeDataVO = new HomeV5CycleDataVO();
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        long currentTimeMillis = System.currentTimeMillis();
        String today = ZoneDateUtil.format(timeZone, currentTimeMillis, DatePatternConst.DATE_PATTERN);

        // 用户算法结果数据
        AlgorithmResultDTO algorithmResultDTO = cacheManager.getAlgorithmResultCheckPeriod(userId);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);

        List<HomeV5CycleDataVO.Cycle> cycles = new ArrayList<>();
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            HomeV5CycleDataVO.Cycle cycle = new HomeV5CycleDataVO.Cycle();
            Integer lenCycle = cycleDataDTO.getLen_cycle();
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle - 1, DatePatternConst.DATE_PATTERN);
            cycle.setCycleIndex(cycleDataDTO.getCycle_index());
            cycle.setDatePeriodStart(datePeriodStart);
            cycle.setDateCycleEnd(dateCycleEnd);
            cycles.add(cycle);
        }
        homeDataVO.setCycles(cycles);

        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        homeDataVO.setUserMode(UserGoalUtil.getUserGoalEnum(loginUserInfoDTO).getValue());
        homeDataVO.setRemindFlag(loginUserInfoDTO.getRemindFlag());

        Integer trackingMenopause = loginUserInfoDTO.getTrackingMenopause();
        homeDataVO.setTrackingMenopause(trackingMenopause == null ? 0 : trackingMenopause);




        TestingPlanVO testingPlanVO = menopauseService.testingPlan();
        homeDataVO.setTestingPlanVO(testingPlanVO);
        if (testingPlanVO != null) {
            homeDataVO.setMenopauseStage(testingPlanVO.getMenopauseStage());
        }

        Integer prevCycleIndex;
        Integer nextCycleIndex;
        CycleDataDTO currentCycleDataDTO;

        //计算今天所属的周期，以及前后周期
        if (cycleIndex != null) {
            int cycleSize = cycleDataDTOS.size();
            if (cycleIndex >= cycleSize) {
                cycleIndex = cycleSize - 1;
            }
            currentCycleDataDTO = cycleDataDTOS.get(cycleIndex);
            prevCycleIndex = cycleIndex > 0 ? cycleIndex - 1 : null;
            nextCycleIndex = cycleIndex >= (cycleSize - 1) ? null : cycleIndex + 1;
        } else {
            //获取当前周期
            currentCycleDataDTO = cycleDataDTOS
                    .stream()
                    .filter(cycleDataDTO ->
                            LocalDateUtil.minusToDay(today, cycleDataDTO.getDate_period_start()) >= 0 &&
                                    LocalDateUtil.minusToDay(today, LocalDateUtil.plusDay(
                                            cycleDataDTO.getDate_period_start(), cycleDataDTO.getLen_cycle(), DatePatternConst.DATE_PATTERN)
                                    ) < 0)
                    .findFirst()
                    .orElse(null);
            if (currentCycleDataDTO == null) {
                //这里说明用户很长时间没用app了，前一个周期取预测周期
                prevCycleIndex = cycleDataDTOS.get(cycleDataDTOS.size() - 1).getCycle_index();
                nextCycleIndex = null;
            } else {
                cycleIndex = currentCycleDataDTO.getCycle_index();
                prevCycleIndex = cycleIndex > 0 ? cycleIndex - 1 : null;
                nextCycleIndex = cycleIndex >= (cycleDataDTOS.size() - 1) ? null : cycleIndex + 1;
            }
        }
        homeDataVO.setPrevCycleIndex(prevCycleIndex);
        homeDataVO.setNextCycleIndex(nextCycleIndex);

        if (currentCycleDataDTO == null) {
            //说明当前是空周期
            homeDataVO.setCycleIndex(null);
            homeDataVO.setCycleStatus(CycleStatusEnum.EXTRA_CYCLE.getStatus());
            homeDataVO.setActionButton(new HomeV5CycleDataVO.ActionButton(HomeActionButtonCodeEnum.SEVEN));
            return homeDataVO;
        }

        String datePeriodStart = currentCycleDataDTO.getDate_period_start();
        Integer lenCycle = currentCycleDataDTO.getLen_cycle();

        homeDataVO.setCycleIndex(currentCycleDataDTO.getCycle_index());
        homeDataVO.setLenCycle(lenCycle);
        Integer cycleStatus = currentCycleDataDTO.getCycle_status();
        homeDataVO.setCycleStatus(cycleStatus);
        homeDataVO.setDatePeriodStart(datePeriodStart);
        homeDataVO.setDatePeriodEnd(currentCycleDataDTO.getDate_period_end());
        homeDataVO.setDateFwStart(currentCycleDataDTO.getDate_FW_start());
        homeDataVO.setDateFwEnd(currentCycleDataDTO.getDate_FW_end());
        homeDataVO.setDateOvulation(currentCycleDataDTO.getDate_ovulation());
        homeDataVO.setDateLhSurge(currentCycleDataDTO.getDate_LH_surge());
        homeDataVO.setOvulationType(currentCycleDataDTO.getOvulation_type());
        homeDataVO.setLenPhase(currentCycleDataDTO.getLen_phase());
        if (CollectionUtils.isNotEmpty(currentCycleDataDTO.getDate_PDG_rise())) {
            currentCycleDataDTO.getDate_PDG_rise().sort(String::compareTo);
            homeDataVO.setDatePdgRises(List.of(currentCycleDataDTO.getDate_PDG_rise().get(0)));
        } else {
            homeDataVO.setDatePdgRises(currentCycleDataDTO.getDate_PDG_rise());
        }
        PregnantRiskVO pregnantRisk = new PregnantRiskVO();
        PregnantRiskDTO pregnant_risk = currentCycleDataDTO.getPregnant_risk();
        if (pregnant_risk != null) {
            pregnantRisk.setHighRiskCDs(pregnant_risk.getHigh_risks());
            pregnantRisk.setMediumRiskCDs(pregnant_risk.getMedium_risks());
            pregnantRisk.setLowRiskCDs(pregnant_risk.getLow_risks());
        }
        homeDataVO.setPregnantRisk(pregnantRisk);

        Integer bindLogFlag = null;
        if (!hormoneDatas.isEmpty()) {
            bindLogFlag = 1;
        } else {
            List<AppUserBindLogEntity> bindLogEntities = appUserBindLogDAO.list10BindLog(userId);
            if (CollectionUtils.isEmpty(bindLogEntities)) {
                bindLogFlag = 0;
            } else {
                bindLogFlag = 1;
            }
        }
        homeDataVO.setBindLogFlag(bindLogFlag);

        HomeV5CycleDataVO.ActionButton actionButton = homeActionButtonManager.buildActionButton(userId,
                hormoneDatas, cycleDataDTOS,
                today, bindLogFlag, homeDataVO.getUserMode());

        homeDataVO.setActionButton(actionButton);

        // check no period
        if (NoPeriodGoalEnum.get(loginUserInfoDTO.getNoPeriodFlag()) != null) {
            NoPeriodHandler.homeCycleData(homeDataVO, currentCycleDataDTO);
        }

        return homeDataVO;
    }


    @Override
    public void cacheActionButton(HomeActionButtonCodeEnum actionButtonCodeEnum) {
        cacheManager.cacheActionButton(actionButtonCodeEnum);
    }

    @Override
    public HomeV5DailyDataVO homeDailyData(String date) {
        HomeV5DailyDataVO homeV5DailyDataVO = new HomeV5DailyDataVO();
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        long currentTimeMillis = System.currentTimeMillis();
        String today = ZoneDateUtil.format(timeZone, currentTimeMillis, DatePatternConst.DATE_PATTERN);

        homeV5DailyDataVO.setDate(date);

        // 用户算法结果数据
        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);

        // 构建每日数据
        buildDailyData(userId, date, today, hormoneDatas, homeV5DailyDataVO);
        // Teting Day
        if (LocalDateUtil.minusToDay(date, today) >= 0) {
            DayTestProductsDTO dayTestProductsDTO = new DayTestProductsDTO();
            dayTestProductsDTO.setDate(date);
            dayTestProductsDTO.setCycleDataDTOS(cycleDataDTOS);
            dayTestProductsDTO.setWandTestBiomarkerDTOS(homeV5DailyDataVO.getHormones());
            List<TestRemindDTO> testingDTOS = TestRemindUtil.getTestRemind(dayTestProductsDTO);
            homeV5DailyDataVO.setTestingDTOS(testingDTOS);
        }
        // 当前周期
        CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(date, cycleDataDTOS);
        // Day In cycle
        Integer cycleStatus = -1;
        String datePeriodStart = currentCycleData.getDate_period_start();
        cycleStatus = currentCycleData.getCycle_status();
        Integer dayInCycle = LocalDateUtil.minusToDay(date, datePeriodStart) + 1;
        homeV5DailyDataVO.setDayInCycle(dayInCycle);
        // Fertility Score
        List<Float> fertilityScoreList = currentCycleData.getFertility_score_list();
        if (CollectionUtils.isNotEmpty(fertilityScoreList)) {
            Float fertilityScore = (dayInCycle < 1 || dayInCycle > fertilityScoreList.size())
                    ? null : fertilityScoreList.get(dayInCycle - 1);
            homeV5DailyDataVO.setFertilityScore(fertilityScore);
        }
        // Date Ovulation
        String dateOvulation = currentCycleData.getDate_ovulation();
        if (StringUtils.isNotBlank(dateOvulation)) {
            int postOvulation = LocalDateUtil.minusToDay(date, dateOvulation);
            boolean isPostOvulation = currentCycleData.getOvulation_type() != null
                    && (currentCycleData.getOvulation_type().equals(OvulationTypeEnum.DETECTED_CONFIRMED.getCode())
                    || currentCycleData.getOvulation_type().equals(OvulationTypeEnum.DETECTED.getCode())
                    || currentCycleData.getOvulation_type().equals(OvulationTypeEnum.CUSTOM.getCode()));
            if (postOvulation >= 0 && isPostOvulation) {
                homeV5DailyDataVO.setPostOvulationDay(postOvulation);
            }
        }
        // Show Word
        HomeShowWordDTO showWordDTO = null;
        boolean contains = List.of(CycleStatusEnum.REAL_CYCLE.getStatus(), CycleStatusEnum.FORECAST_CYCLE.getStatus(), CycleStatusEnum.PREGNANCY_CYCLE_FIRST_STAGE.getStatus(), CycleStatusEnum.PREGNANCY_CYCLE_SECOND_STAGE.getStatus(), CycleStatusEnum.PREGNANCY_CYCLE_THIRD_STAGE.getStatus(), CycleStatusEnum.PREGNANCY_CYCLE.getStatus(), CycleStatusEnum.ABNORMAL_PREGNANCY_CYCLE.getStatus()).contains(cycleStatus);
        if (contains) {
            showWordDTO = CycleShowWordUtil.buildShowWord(cycleDataDTOS, date, today);
        }
        homeV5DailyDataVO.setShowWordDTO(showWordDTO);
        // check no period
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        if (NoPeriodGoalEnum.get(loginUserInfoDTO.getNoPeriodFlag()) != null) {
            NoPeriodHandler.homeDailyData(currentCycleData, homeV5DailyDataVO);
        }
        SpringContextHolder.publishEvent(new EnterHomePageEvent(userId));

        return homeV5DailyDataVO;
    }

    private void buildDailyData(Long userId, String date, String today,
                                List<HormoneDTO> hormoneDataList, HomeV5DailyDataVO homeV5DailyDataVO) {
        // 用户的日记选项配置
        CustomLogConfigDTO customLogConfigDTO = userCustomLogManager.configInfo(userId);
        String tempUnit = customLogConfigDTO.getTempUnit();
        homeV5DailyDataVO.setTempUnit(tempUnit);

        // 首页每天数据
        List<WandTestDataDTO> wandDayTestDataDTOS = new ArrayList<>();
        List<HormoneDTO> dailyHormoneDatas = hormoneDataList.stream()
                                                            .filter(hormoneData -> date.equals(LocalDateUtil.dateTime2Date(hormoneData.getTest_time())))
                                                            .collect(Collectors.toList());
        WandTestDataDTO wandDayTestDataDTO = new WandTestDataDTO();
        wandDayTestDataDTO.setDate(date);
        wandDayTestDataDTO.setHormoneDTO(dailyHormoneDatas);
        wandDayTestDataDTOS.add(wandDayTestDataDTO);

        Map<String, List<WandTestBiomarkerDTO>> wandTestBiomarkerMap = bluetoothProvider.getDayWandBiomarkerData(wandDayTestDataDTOS, userId).getData()
                                                                                        .stream().collect(Collectors.toMap(WandDiaryTestBiomarkerDTO::getDate, WandDiaryTestBiomarkerDTO::getWandTestBiomarkerDTOS));
        List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS = wandTestBiomarkerMap.get(date);

        List<WandTestBiomarkerDTO> pendingWandTestBiomarkerDTOS = manualDataManager.getPendingWandTestBiomarkerDTOS(userId, date);
        if (!pendingWandTestBiomarkerDTOS.isEmpty()) {
            wandTestBiomarkerDTOS.addAll(pendingWandTestBiomarkerDTOS);
        }

        // BBT
        if (LocalDateUtil.minusToDay(date, today) <= 0) {
            List<TemperatureDTO> temperatureDTOS = userDiaryLogManager.listTemperatureDTO(userId, date);
            List<TemperatureDTO> filterTemperatureDTOS = temperatureDTOS.stream()
                                                                        .filter(temperatureDTO -> temperatureDTO.getType() != 0)
                                                                        .filter(temperatureDTO -> StringUtils.isBlank(temperatureDTO.getEcode()))
                                                                        .collect(Collectors.toList());
            for (TemperatureDTO temperatureDTO : filterTemperatureDTOS) {
                WandTestBiomarkerDTO wandTestBiomarkerDTO = new WandTestBiomarkerDTO("BBT", temperatureDTO.getTempTime(), temperatureDTO.getEcode());
                String testValue;
                if (TempUnitEnum.C.getValue().equals(tempUnit)) {
                    testValue = temperatureDTO.getTempC().setScale(2, RoundingMode.HALF_UP).floatValue() + "";
                } else {
                    testValue = temperatureDTO.getTempF().setScale(2, RoundingMode.HALF_UP).floatValue() + "";
                }
                wandTestBiomarkerDTO.setTestValue(testValue);
                wandTestBiomarkerDTOS.add(wandTestBiomarkerDTO);
            }
        }
        homeV5DailyDataVO.setHormones(wandTestBiomarkerDTOS);
    }
}



