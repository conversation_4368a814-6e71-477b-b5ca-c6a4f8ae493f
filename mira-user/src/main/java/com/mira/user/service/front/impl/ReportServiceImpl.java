package com.mira.user.service.front.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.TestDataDTO;
import com.mira.api.bluetooth.dto.menopause.MenopauseResultDTO;
import com.mira.api.bluetooth.dto.report.*;
import com.mira.api.bluetooth.dto.wand.WandTestBiomarkerDTO;
import com.mira.api.bluetooth.dto.wand.WandTestDataDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.enums.BBTModeErrorCodeEnum;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.provider.IBluetoothProvider;
import com.mira.api.bluetooth.provider.IMenopauseProvider;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.dto.user.diary.CustomLogConfigDTO;
import com.mira.api.user.dto.user.diary.DateSymptomDTO;
import com.mira.api.user.dto.user.diary.UserMedicineDTO;
import com.mira.api.user.dto.user.diary.UserSymptomDTO;
import com.mira.api.user.enums.NoPeriodGoalEnum;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.api.user.enums.daily.DailyStatusSexEnum;
import com.mira.api.user.enums.daily.DailySymptomEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.BiomarkerEnum;
import com.mira.core.consts.enums.BiomarkerNameEnum;
import com.mira.core.consts.enums.TempUnitEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.*;
import com.mira.redis.cache.RedisComponent;
import com.mira.user.controller.vo.chart.ChartLineVO;
import com.mira.user.controller.vo.cycle.CycleAnalysisVO;
import com.mira.user.controller.vo.cycle.CycleDataVO;
import com.mira.user.controller.vo.report.ReportVO;
import com.mira.user.dal.dao.*;
import com.mira.user.dal.entity.*;
import com.mira.user.dto.chart.TemperatureChartDTO;
import com.mira.user.dto.report.ReportAppWandsMetric2DTO;
import com.mira.user.dto.report.ReportAppWandsMetricDTO;
import com.mira.user.dto.report.ReportAppWandsMetricResultDTO;
import com.mira.user.enums.chart.WandsMetricTypeEnum;
import com.mira.user.enums.report.AlgorithmReportSummaryDataTypeEnum;
import com.mira.user.enums.report.CycleSelectTypeEnum;
import com.mira.user.exception.UserException;
import com.mira.user.handler.noperiod.NoPeriodHandler;
import com.mira.user.properties.CacheExpireProperties;
import com.mira.user.service.front.IReportService;
import com.mira.user.service.manager.*;
import com.mira.web.properties.SysDictProperties;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Report 接口实现
 *
 * <AUTHOR>
 */
@Service
public class ReportServiceImpl implements IReportService {
    @Resource
    private AppUserPeriodDAO appUserPeriodDAO;
    @Resource
    private AppUserDiaryDAO appUserDiaryDAO;
    @Resource
    private AppUserDiarySymptomsDAO appUserDiarySymptomsDAO;
    @Resource
    private AppUserDiaryMedicationsDAO appUserDiaryMedicationsDAO;
    @Resource
    private AppUserTemperatureDAO appUserTemperatureDAO;
    @Resource
    private ReportAppWandsMetricDAO reportAppWandsMetricDAO;
    @Resource
    private SysReportSummaryTemplateDAO sysReportSummaryTemplateDAO;

    @Resource
    private CacheManager cacheManager;
    @Resource
    private UserCustomLogManager userCustomLogManager;
    @Resource
    private ChartManager chartManager;
    @Resource
    private CycleManager cycleManager;
    @Resource
    private AlgorithmCallManager algorithmCallManager;

    @Resource
    private CacheExpireProperties cacheExpireProperties;
    @Resource
    private SysDictProperties sysDictProperties;

    @Resource
    private RedisComponent redisComponent;

    @Resource
    private IBluetoothProvider bluetoothProvider;
    @Resource
    private ISsoProvider ssoProvider;
    @Resource
    private IMenopauseProvider menopauseProvider;

    @Resource
    private AppUserMenopauseSurveyDAO appUserMenopauseSurveyDAO;

    public static final BigDecimal timeStampOneDay = new BigDecimal(24 * 3600 * 1000);

    @Override
    public List<CycleSelectTypeEnum.Result> selectType(String requestType) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        AlgorithmResultDTO cacheAlgorithmResult = cacheManager.getCacheAlgorithmResult(userId);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(cacheAlgorithmResult.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(cacheAlgorithmResult.getHormoneData(), HormoneDTO.class);

        // 根据selectType和CycleDataDTOS确定时间范围
        List<CycleDataDTO> filterCycleDataDTOS = cycleDataDTOS
                .stream()
                .filter(cycleDataDTO -> cycleDataDTO.getCycle_status().equals(CycleStatusEnum.REAL_CYCLE.getStatus())
                        || cycleDataDTO.getCycle_status().equals(CycleStatusEnum.FORECAST_CYCLE.getStatus()))
                .collect(Collectors.toList());
        int selectCycles = 0;
        for (CycleDataDTO cycleDataDTO : filterCycleDataDTOS) {
            String startDate = cycleDataDTO.getDate_period_start();
            String endDate = LocalDateUtil.plusDay(cycleDataDTO.getDate_period_start(), cycleDataDTO.getLen_cycle(), DatePatternConst.DATE_PATTERN);
            List<HormoneDTO> selectHormoneDatas =
                    hormoneDatas
                            .stream()
                            .filter(hormoneDTO -> LocalDateUtil.minusToDay(LocalDateUtil.dateTime2Date(hormoneDTO.getTest_time()), startDate) >= 0
                                    && LocalDateUtil.minusToDay(LocalDateUtil.dateTime2Date(hormoneDTO.getTest_time()), endDate) < 0)
                            .filter(hormoneDTO -> hormoneDTO.getFlag() == 1)
                            .collect(Collectors.toList());

            List<Long> hideHcgValueUserIds = sysDictProperties.getHideHcgValueUserIds();
            if (hideHcgValueUserIds != null && hideHcgValueUserIds.contains(userId)) {
                selectHormoneDatas.removeIf(
                        hormoneDTO -> hormoneDTO.getTest_results().getWand_type().equals(WandTypeEnum.HCG.getInteger())
                );
            }
            if (!selectHormoneDatas.isEmpty()) {
                selectCycles++;
            }
        }
        List<CycleSelectTypeEnum.Result> selectTypes = new ArrayList<>();
        if (selectCycles == 2) {
            selectTypes.add(CycleSelectTypeEnum.transfer(CycleSelectTypeEnum.TWO));
        }
        if (selectCycles >= 3) {
            selectTypes.add(CycleSelectTypeEnum.transfer(CycleSelectTypeEnum.THREE));
        }
        if ("mira-check".equals(requestType)) {
            if (selectCycles >= 4) {
                selectTypes.add(CycleSelectTypeEnum.transfer(CycleSelectTypeEnum.FOUR));
            }
            if (selectCycles >= 5) {
                selectTypes.add(CycleSelectTypeEnum.transfer(CycleSelectTypeEnum.FIVE));
            }
        }
        if (selectCycles >= 6) {
            selectTypes.add(CycleSelectTypeEnum.transfer(CycleSelectTypeEnum.SIX));
        }
        if ("mira-check".equals(requestType)) {
            if (selectCycles >= 7) {
                selectTypes.add(CycleSelectTypeEnum.transfer(CycleSelectTypeEnum.SEVEN));
            }
            if (selectCycles >= 8) {
                selectTypes.add(CycleSelectTypeEnum.transfer(CycleSelectTypeEnum.EIGHT));
            }
            if (selectCycles >= 9) {
                selectTypes.add(CycleSelectTypeEnum.transfer(CycleSelectTypeEnum.NINE));
            }
            if (selectCycles >= 10) {
                selectTypes.add(CycleSelectTypeEnum.transfer(CycleSelectTypeEnum.TEN));
            }
            if (selectCycles >= 11) {
                selectTypes.add(CycleSelectTypeEnum.transfer(CycleSelectTypeEnum.ELEVEN));
            }
            if (selectCycles >= 13) {
                selectTypes.add(CycleSelectTypeEnum.transfer(CycleSelectTypeEnum.ALL));
            }
        }
        if (selectCycles >= 12) {
            selectTypes.add(CycleSelectTypeEnum.transfer(CycleSelectTypeEnum.TWELVE));
        }
        return selectTypes;
    }

    @Override
    public ReportVO report(CycleSelectTypeEnum cycleSelectTypeEnum) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        ReportVO reportVO = new ReportVO();
        String cacheKey = RedisCacheKeyConst.USER_REPORT + userId + ":" + cycleSelectTypeEnum.getCode();
        if (redisComponent.exists(cacheKey)) {
            return redisComponent.get(cacheKey, ReportVO.class);
        }

        AlgorithmResultDTO cacheAlgorithmResult = cacheManager.getCacheAlgorithmResult(userId);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(cacheAlgorithmResult.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(cacheAlgorithmResult.getHormoneData(), HormoneDTO.class);

        String timeZone = cacheAlgorithmResult.getTimeZone();
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        List<HormoneDTO> validHormoneDTOS = hormoneDatas.stream().filter(hormoneDTO -> hormoneDTO.getFlag() == 1).collect(Collectors.toList());

        List<Long> hideHcgValueUserIds = sysDictProperties.getHideHcgValueUserIds();
        if (hideHcgValueUserIds != null && hideHcgValueUserIds.contains(userId)) {
            validHormoneDTOS.removeIf(
                    hormoneDTO -> hormoneDTO.getTest_results().getWand_type().equals(WandTypeEnum.HCG.getInteger())
            );
        }

        // selectedCycles
        List<CycleDataDTO> selectedCycles = buildSelectedCycles(cycleSelectTypeEnum, today, cycleDataDTOS, validHormoneDTOS);

        // analysisCycles
        List<CycleDataDTO> analysisCycles = buildAnalysisCycles(cycleDataDTOS, selectedCycles, today);
        String initialDay = selectedCycles.get(0).getDate_period_start();
        String endDay1;
        if (analysisCycles.size() >= 1) {
            endDay1 = LocalDateUtil.plusDay(analysisCycles.get(analysisCycles.size() - 1).getDate_period_start(), analysisCycles.get(0).getLen_cycle(), DatePatternConst.DATE_PATTERN);
        } else {
            endDay1 = today;
        }
        // 选定cycle第一天到今天区间内的hormone数据
        List<HormoneDTO> selectHormoneDatas =
                validHormoneDTOS.stream()
                                .filter(hormoneDTO -> LocalDateUtil.minusToDay(LocalDateUtil.dateTime2Date(hormoneDTO.getTest_time()), initialDay) >= 0
                                        && LocalDateUtil.minusToDay(LocalDateUtil.dateTime2Date(hormoneDTO.getTest_time()), today) <= 0)
                                .collect(Collectors.toList());
        List<HormoneDTO> analysisHormoneDatas =
                validHormoneDTOS.stream()
                                .filter(hormoneDTO -> LocalDateUtil.minusToDay(LocalDateUtil.dateTime2Date(hormoneDTO.getTest_time()), initialDay) >= 0
                                        && LocalDateUtil.minusToDay(LocalDateUtil.dateTime2Date(hormoneDTO.getTest_time()), endDay1) <= 0)
                                .collect(Collectors.toList());

        // 温度及温度单位
        CustomLogConfigDTO appUserCustomLogConfigDTO = userCustomLogManager.configInfo(userId);
        String tempUnit = appUserCustomLogConfigDTO.getTempUnit();
        reportVO.setTempUnit(tempUnit);
        List<TemperatureChartDTO> allSelectTemperatureDatas = new ArrayList<>();
        List<AppUserTemperatureEntity> appUserTemperatureEntities = appUserTemperatureDAO.listByUserIdAndBetweenDay(userId, initialDay, today);
        if (CollectionUtils.isNotEmpty(appUserTemperatureEntities)) {
            for (AppUserTemperatureEntity appUserTemperatureEntity : appUserTemperatureEntities) {
                String modeError = appUserTemperatureEntity.getModeError();
                if (!BBTModeErrorCodeEnum.NORMAL.getCode().equals(modeError)) {
                    continue;
                }
                TemperatureChartDTO temperatureChartDTO = new TemperatureChartDTO();
                if (TempUnitEnum.C.getValue().equals(tempUnit)) {
                    temperatureChartDTO.setValue(appUserTemperatureEntity.getTempC().floatValue());
                } else {
                    temperatureChartDTO.setValue(appUserTemperatureEntity.getTempF().floatValue());
                }
                BeanUtil.copyProperties(appUserTemperatureEntity, temperatureChartDTO);
                temperatureChartDTO.setTestTime(appUserTemperatureEntity.getTempTime());
                allSelectTemperatureDatas.add(temperatureChartDTO);
            }
        }

        // baseInfo
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        setBaseInfo(reportVO, loginUserInfoDTO, initialDay, today);
        Integer trackingMenopause = loginUserInfoDTO.getTrackingMenopause();
        reportVO.setTrackingMenopause(trackingMenopause == null ? 0 : trackingMenopause);

        if (trackingMenopause != null && trackingMenopause == 1) {
            MenopauseResultDTO menopauseResultDTO = menopauseProvider.getMenopauseResult(userId,
                    AlgorithmRequestTypeEnum.GET_MENOPAUSE_RESULT_report).getData();
            reportVO.setMenopauseStage(menopauseResultDTO.getDefineStage());
            reportVO.setDefineStageDate(menopauseResultDTO.getDefineStageDate());

            AppUserMenopauseSurveyEntity menopauseSurveyEntity = appUserMenopauseSurveyDAO.getByUserId(userId);
            if (menopauseSurveyEntity != null) {
                Integer everPregnant = menopauseSurveyEntity.getEverPregnant();
                Integer takeMedication = menopauseSurveyEntity.getTakeMedication();
                String medications = menopauseSurveyEntity.getMedications();
                String procedures = menopauseSurveyEntity.getProcedures();
                Integer hrt = menopauseSurveyEntity.getHrt();
                String hrtTypes = menopauseSurveyEntity.getHrtTypes();
                Integer physicallyActive = menopauseSurveyEntity.getPhysicallyActive();
                String physicallyActiveTypes = menopauseSurveyEntity.getPhysicallyActiveTypes();

                ReportVO.MenopauseSurveyInfo menopauseSurveyInfo = new ReportVO.MenopauseSurveyInfo();
                menopauseSurveyInfo.setConditions(StringListUtil.strToIntegerList(loginUserInfoDTO.getConditions(), ","));
                menopauseSurveyInfo.setEverPregnant(everPregnant);
                menopauseSurveyInfo.setTakeMedication(takeMedication);
                if (StringUtils.isNotBlank(medications)){
                    menopauseSurveyInfo.setMedications(JsonUtil.toArray(medications, UserMedicineDTO.class));
                }
                menopauseSurveyInfo.setProcedures(StringListUtil.strToIntegerList(procedures, ","));
                menopauseSurveyInfo.setHrt(hrt);
                menopauseSurveyInfo.setHrtTypes(StringListUtil.strToIntegerList(hrtTypes, ","));
                menopauseSurveyInfo.setPhysicallyActive(physicallyActive);
                menopauseSurveyInfo.setPhysicallyActiveTypes(StringListUtil.strToIntegerList(physicallyActiveTypes, ","));

                reportVO.setMenopauseSurveyInfo(menopauseSurveyInfo);
            }

        }


        // legends
        List<ChartLineVO.Legend> legends = chartManager.buildLegend(selectHormoneDatas, allSelectTemperatureDatas);
        reportVO.setLegend(legends.stream().filter(legend -> legend.getType() == 1).collect(Collectors.toList()));

        // SymptomDTOS
        List<AppUserDiarySymptomsEntity> appUserDiarySymptomsEntities = appUserDiarySymptomsDAO.listByUserIdAndBetweenDay(userId, initialDay, today);
        List<DateSymptomDTO> selectUserSymptomDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(appUserDiarySymptomsEntities)) {
            for (AppUserDiarySymptomsEntity appUserDiarySymptomsEntity : appUserDiarySymptomsEntities) {
                String symptomsStr = appUserDiarySymptomsEntity.getSymptoms();
                List<UserSymptomDTO> appUserSymptomDTOS = JsonUtil.toArray(symptomsStr, UserSymptomDTO.class);
                DateSymptomDTO dateSymptomDTO = new DateSymptomDTO();
                dateSymptomDTO.setSymptomDTOS(appUserSymptomDTOS);
                dateSymptomDTO.setDate(appUserDiarySymptomsEntity.getDiaryDayStr());
                selectUserSymptomDTOS.add(dateSymptomDTO);
            }
        }
        reportVO.setDateSymptomDTOS(selectUserSymptomDTOS);

        // Hormone data comparison :At least one testing hormone data
        List<CycleDataDTO> comparisonCycles = buildComparisonCycles(validHormoneDTOS, selectedCycles);

        // cycleCompare
        List<CycleDataVO> dataInCompareCycles = chartManager.buildDataInCycle(today, comparisonCycles, selectHormoneDatas,
                allSelectTemperatureDatas, new ArrayList<>(), new HashMap<>());
        chartManager.appendExtraTemperatureCycle(allSelectTemperatureDatas, dataInCompareCycles, comparisonCycles.get(comparisonCycles.size() - 1));
        List<ReportAppWandsMetricEntity> reportAppWandsMetricEntities = reportAppWandsMetricDAO.listInMarker(Arrays.asList(1, 3, 9));
        Map<String, ReportAppWandsMetricDTO> wandsMetricDTOMap = buildWandsMetricDTOMap(reportAppWandsMetricEntities);
        ReportVO.CycleCompare cycleCompare = buildCycleCompare(dataInCompareCycles, wandsMetricDTOMap);
        reportVO.setCycleCompare(cycleCompare);

        // Hormone graph and daily log by cycle:At least one testing hormone or symptom
        List<CycleDataDTO> graphCycles = buildGraphCycles(cycleDataDTOS, selectUserSymptomDTOS, comparisonCycles);

        // cycles:单个周期的chart展示
        List<ReportVO.Cycle> cycles = buildCycles(selectHormoneDatas, today, allSelectTemperatureDatas, graphCycles);
        reportVO.setCycles(cycles);

        // sexDate
        List<Long> sexDateList = new ArrayList<>();
        List<AppUserDiaryEntity> appUserDiaryEntities = appUserDiaryDAO.listSexRecordsByUserId(userId);
        if (CollectionUtils.isNotEmpty(appUserDiaryEntities)) {
            appUserDiaryEntities.forEach(appUserDiaryEntity -> {
                if (DailyStatusSexEnum.C.getValue().equals(appUserDiaryEntity.getSex())
                        || DailyStatusSexEnum.S.getValue().equals(appUserDiaryEntity.getSex())) {
                    sexDateList.add(appUserDiaryEntity.getDiaryDay());
                }
            });
        }

        // cycleAnalysis
        ReportReturnDTO reportReturnDTO = algorithmCallManager.report(appUserPeriodDAO.getByUserId(userId), loginUserInfoDTO,
                selectedCycles, analysisHormoneDatas, selectUserSymptomDTOS);

        CycleAnalysisVO cycleAnalysisVO = cycleManager.buildCycleAnalysisVO(userId, reportReturnDTO.getUser_mode(),
                trackingMenopause,
                reportReturnDTO.getCycle_analysis(), analysisCycles, hormoneDatas, sexDateList);
        reportVO.setCycleAnalysisVO(cycleAnalysisVO);

        // generalSummary
        List<ReportSummary> summaries = reportReturnDTO.getSummary();
        List<String> generalSummaries = this.buildGeneralSummary(summaries);
        reportVO.setGeneralSummary(generalSummaries);

        // compareShapes
        List<ReportVO.CompareShape> compareShapes = buildCompareShapes(comparisonCycles);
        reportVO.setCompareShapes(buildCompareShapes(comparisonCycles));
        List<Integer> compareShapeIndexes =
                compareShapes.stream().map(ReportVO.CompareShape::getCycleIndex).collect(Collectors.toList());

        // reportAppWandsMetric2DTOMap
        ReportAppWandsMetricResultDTO reportAppWandsMetricResultDTO = buildReportAppWandsMetricResultDTO(reportReturnDTO.getCycle_analysis_raw(), compareShapeIndexes);
        reportVO.setReportAppWandsMetricResultDTO(reportAppWandsMetricResultDTO);

        // valueRanges
        WandTestDataDTO wandDayTestDataDTO = new WandTestDataDTO();
        wandDayTestDataDTO.setHormoneDTO(hormoneDatas);
        wandDayTestDataDTO.setFilter(Boolean.FALSE);
        List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS = bluetoothProvider.getDayWandBiomarkerData(wandDayTestDataDTO, userId).getData();
        List<WandTestBiomarkerDTO> filteredWandTestBiomarkerDTOS =
                wandTestBiomarkerDTOS.stream()
                                     //                                     .filter(wandTestBiomarkerDTO -> StringUtils.isBlank(wandTestBiomarkerDTO.getEcode()))
                                     .collect(Collectors.toList());
        List<ReportVO.ValueRange> valueRanges = buildValueRanges(legends, filteredWandTestBiomarkerDTOS, allSelectTemperatureDatas);
        reportVO.setValueRanges(valueRanges);

        // dataRecords
        List<ReportVO.DataRecord> dataRecords = buildDataRecords(userId, filteredWandTestBiomarkerDTOS, initialDay, today);
        reportVO.setDataRecords(dataRecords);

        if (NoPeriodGoalEnum.get(loginUserInfoDTO.getNoPeriodFlag()) != null) {
            reportVO.setNoPeriod(1);
        }

        redisComponent.setEx(cacheKey, reportVO, cacheExpireProperties.getReport(), TimeUnit.MINUTES);

        return reportVO;
    }

    private Map<String, ReportAppWandsMetricDTO> buildWandsMetricDTOMap(List<ReportAppWandsMetricEntity> reportAppWandsMetricEntities) {
        Map<String, ReportAppWandsMetricDTO> biomarkerResultMap = new HashMap<>();
        ReportAppWandsMetricDTO lhResults = new ReportAppWandsMetricDTO();
        ReportAppWandsMetricDTO e3gResults = new ReportAppWandsMetricDTO();
        ReportAppWandsMetricDTO pdgResults = new ReportAppWandsMetricDTO();

        List<ReportAppWandsMetricDTO.Metric> lhMax = new ArrayList<>();
        List<ReportAppWandsMetricDTO.Metric> lhMin = new ArrayList<>();

        List<ReportAppWandsMetricDTO.Metric> e3gMax = new ArrayList<>();
        List<ReportAppWandsMetricDTO.Metric> e3gMin = new ArrayList<>();

        List<ReportAppWandsMetricDTO.Metric> pdgMax = new ArrayList<>();
        List<ReportAppWandsMetricDTO.Metric> pdgMin = new ArrayList<>();

        for (ReportAppWandsMetricEntity wandsMetricEntity : reportAppWandsMetricEntities) {
            Integer biomarker = wandsMetricEntity.getMarker();
            Long ovuIndex = wandsMetricEntity.getIndex();
            Integer metricType = wandsMetricEntity.getMetricType();
            Float value = wandsMetricEntity.getMetricValue().floatValue();
            if (Objects.equals(biomarker, BiomarkerEnum.LH.getBiomarker())) {
                ReportAppWandsMetricDTO.Metric metric = new ReportAppWandsMetricDTO.Metric();
                metric.setIndex(ovuIndex);
                metric.setValue(value);
                if (metricType == WandsMetricTypeEnum.LOWER_5.getType()) {
                    lhMin.add(metric);
                } else if (metricType == WandsMetricTypeEnum.UPPER_95.getType()) {
                    lhMax.add(metric);
                }
            } else if (Objects.equals(biomarker, BiomarkerEnum.E3G.getBiomarker())) {
                ReportAppWandsMetricDTO.Metric metric = new ReportAppWandsMetricDTO.Metric();
                metric.setIndex(ovuIndex);
                metric.setValue(value);
                if (metricType == WandsMetricTypeEnum.LOWER_5.getType()) {
                    e3gMin.add(metric);
                } else if (metricType == WandsMetricTypeEnum.UPPER_95.getType()) {
                    e3gMax.add(metric);
                }
            } else if (Objects.equals(biomarker, BiomarkerEnum.PDG.getBiomarker())) {
                ReportAppWandsMetricDTO.Metric metric = new ReportAppWandsMetricDTO.Metric();
                metric.setIndex(ovuIndex);
                metric.setValue(value);
                if (metricType == WandsMetricTypeEnum.LOWER_5.getType()) {
                    pdgMin.add(metric);
                } else if (metricType == WandsMetricTypeEnum.UPPER_95.getType()) {
                    pdgMax.add(metric);
                }
            }
        }

        lhResults.setMin(lhMin.stream().sorted(Comparator.comparing(ReportAppWandsMetricDTO.Metric::getIndex)).collect(Collectors.toList()));
        lhResults.setMax(lhMax.stream().sorted(Comparator.comparing(ReportAppWandsMetricDTO.Metric::getIndex)).collect(Collectors.toList()));
        e3gResults.setMin(e3gMin.stream().sorted(Comparator.comparing(ReportAppWandsMetricDTO.Metric::getIndex)).collect(Collectors.toList()));
        e3gResults.setMax(e3gMax.stream().sorted(Comparator.comparing(ReportAppWandsMetricDTO.Metric::getIndex)).collect(Collectors.toList()));
        pdgResults.setMin(pdgMin.stream().sorted(Comparator.comparing(ReportAppWandsMetricDTO.Metric::getIndex)).collect(Collectors.toList()));
        pdgResults.setMax(pdgMax.stream().sorted(Comparator.comparing(ReportAppWandsMetricDTO.Metric::getIndex)).collect(Collectors.toList()));
        biomarkerResultMap.put(BiomarkerNameEnum.LH.getName(), lhResults);
        biomarkerResultMap.put(BiomarkerNameEnum.E3G.getName(), e3gResults);
        biomarkerResultMap.put(BiomarkerNameEnum.PDG.getName(), pdgResults);
        return biomarkerResultMap;
    }

    private ReportAppWandsMetricResultDTO buildReportAppWandsMetricResultDTO(ReportCycleAnalysisRaw cycle_analysis_raw,
                                                                             List<Integer> compareShapeIndexes) {
        ReportAppWandsMetricResultDTO reportAppWandsMetricResultDTO = this.buildReportAppWandsMetricResultDTO();
        for (ReportAppWandsMetric2DTO reportAppWandsMetric2DTO : reportAppWandsMetricResultDTO.getCycleLengthResults()) {
            if (reportAppWandsMetric2DTO.getMetricType() != WandsMetricTypeEnum.RATIO.getType()) {
                continue;
            }
            for (ReportCycleValue result : cycle_analysis_raw.getCycle_len()) {
                Integer cycleIndex = result.getCycle_index();
                if (result.getValue().equals(reportAppWandsMetric2DTO.getIndex().intValue()) && compareShapeIndexes.contains(cycleIndex)) {
                    reportAppWandsMetric2DTO.getCycleIndexes().add(cycleIndex);
                }
            }
        }
        for (ReportAppWandsMetric2DTO reportAppWandsMetric2DTO : reportAppWandsMetricResultDTO.getLutealLengthResults()) {
            if (reportAppWandsMetric2DTO.getMetricType() != WandsMetricTypeEnum.RATIO.getType()) {
                continue;
            }
            for (ReportCycleValue result : cycle_analysis_raw.getLuteal_phases()) {
                if (result.getValue().equals(reportAppWandsMetric2DTO.getIndex().intValue())) {
                    reportAppWandsMetric2DTO.getCycleIndexes().add(result.getCycle_index());
                }
            }
        }
        for (ReportAppWandsMetric2DTO reportAppWandsMetric2DTO : reportAppWandsMetricResultDTO.getFollicularLengthResults()) {
            if (reportAppWandsMetric2DTO.getMetricType() != WandsMetricTypeEnum.RATIO.getType()) {
                continue;
            }
            for (ReportCycleValue result : cycle_analysis_raw.getFolicular_phases()) {
                if (result.getValue().equals(reportAppWandsMetric2DTO.getIndex().intValue())) {
                    reportAppWandsMetric2DTO.getCycleIndexes().add(result.getCycle_index());
                }
            }
        }
        return reportAppWandsMetricResultDTO;
    }

    private ReportAppWandsMetricResultDTO buildReportAppWandsMetricResultDTO() {
        List<ReportAppWandsMetricEntity> wandsMetricEntityList = reportAppWandsMetricDAO.listInMarker(Arrays.asList(100, 101, 102));
        ReportAppWandsMetricResultDTO reportAppWandsMetricResultDTO = new ReportAppWandsMetricResultDTO();

        List<ReportAppWandsMetric2DTO> cycleLengthResults = new ArrayList<>();
        List<ReportAppWandsMetric2DTO> follicularLengthResults = new ArrayList<>();
        List<ReportAppWandsMetric2DTO> lutealLengthResults = new ArrayList<>();

        for (ReportAppWandsMetricEntity wandsMetricEntity : wandsMetricEntityList) {
            Integer biomarker = wandsMetricEntity.getMarker();
            ReportAppWandsMetric2DTO reportAppWandsMetric2DTO = new ReportAppWandsMetric2DTO();
            reportAppWandsMetric2DTO.setIndex(wandsMetricEntity.getIndex());
            reportAppWandsMetric2DTO.setMetricType(wandsMetricEntity.getMetricType());
            reportAppWandsMetric2DTO.setMetricValue(wandsMetricEntity.getMetricValue().floatValue());
            if (biomarker == 100) {
                cycleLengthResults.add(reportAppWandsMetric2DTO);
            } else if (biomarker == 101) {
                follicularLengthResults.add(reportAppWandsMetric2DTO);
            } else if (biomarker == 102) {
                lutealLengthResults.add(reportAppWandsMetric2DTO);
            }
        }
        reportAppWandsMetricResultDTO.setCycleLengthResults(cycleLengthResults);
        reportAppWandsMetricResultDTO.setFollicularLengthResults(follicularLengthResults);
        reportAppWandsMetricResultDTO.setLutealLengthResults(lutealLengthResults);
        return reportAppWandsMetricResultDTO;
    }

    private List<ReportVO.CompareShape> buildCompareShapes(List<CycleDataDTO> comparisonCycles) {
        List<ReportVO.CompareShape> compareShapes = new ArrayList<>();
        for (int i = 0; i < comparisonCycles.size(); i++) {
            ReportVO.CompareShape compareShape = new ReportVO.CompareShape();
            compareShape.setCycleIndex(comparisonCycles.get(i).getCycle_index());
            compareShape.setShapeIndex(i);
            String datePeriodStart = comparisonCycles.get(i).getDate_period_start();
            compareShape.setDatePeriodStart(datePeriodStart);
            compareShape.setDateCycleEnd(LocalDateUtil.plusDay(datePeriodStart, comparisonCycles.get(i).getLen_cycle(), DatePatternConst.DATE_PATTERN));
            compareShapes.add(compareShape);
        }
        return compareShapes;
    }


    private List<String> buildGeneralSummary(List<ReportSummary> summaries) {
        String language = ContextHolder.get(HeaderConst.LOCAL_LANGUAGE);
        List<String> generalSummaries = new ArrayList<>();
        for (ReportSummary summary : summaries) {
            SysReportSummaryTemplateEntity reportSummaryTemplate = sysReportSummaryTemplateDAO.getByModelIdAndLanguage(Long.valueOf(summary.getModel_id()), language);
            if (Objects.isNull(reportSummaryTemplate)) {
                continue;
            }
            String template = reportSummaryTemplate.getTemplate();
            String generalSummary = buildTemplate(template, summary.getModel_data(), summary.getModel_id());
            generalSummaries.add(generalSummary);
        }
        return generalSummaries;
    }

    private List<ReportVO.Cycle> buildCycles(List<HormoneDTO> selectHormoneDatas, String today,
                                             List<TemperatureChartDTO> allSelectTemperatureDatas, List<CycleDataDTO> graphCycles) {
        List<CycleDataVO> dataInCycles = chartManager.buildDataInCycle(today, graphCycles, selectHormoneDatas,
                allSelectTemperatureDatas, new ArrayList<>(), new HashMap<>());
        chartManager.appendExtraTemperatureCycle(allSelectTemperatureDatas, dataInCycles, graphCycles.get(graphCycles.size() - 1));
        List<ReportVO.Cycle> cycles = new ArrayList<>();
        for (CycleDataVO dataInCycle : dataInCycles) {
            ReportVO.Cycle cycle = new ReportVO.Cycle();
            BeanUtils.copyProperties(dataInCycle, cycle);
            cycles.add(cycle);
        }
        return cycles;
    }

    private static List<CycleDataDTO> buildGraphCycles(List<CycleDataDTO> cycleDataDTOS, List<DateSymptomDTO> selectUserSymptomDTOS, List<CycleDataDTO> comparisonCycles) {
        List<CycleDataDTO> graphCycles = new ArrayList<>(comparisonCycles);
        List<Integer> graphCycleIndexes = graphCycles.stream().map(CycleDataDTO::getCycle_index).collect(Collectors.toList());
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, cycleDataDTO.getLen_cycle(), DatePatternConst.DATE_PATTERN);
            List<DateSymptomDTO> graphDateSymptomDTOs = selectUserSymptomDTOS
                    .stream()
                    .filter(dateSymptomDTO -> LocalDateUtil.minusToDay(dateSymptomDTO.getDate(), datePeriodStart) >= 0
                            && LocalDateUtil.minusToDay(dateSymptomDTO.getDate(), dateCycleEnd) < 0).collect(Collectors.toList());
            Integer cycleIndex = cycleDataDTO.getCycle_index();
            if (!graphCycleIndexes.contains(cycleIndex) && !graphDateSymptomDTOs.isEmpty()) {
                graphCycles.add(cycleDataDTO);
            }
        }
        return graphCycles.stream().sorted(Comparator.comparing(CycleDataDTO::getCycle_index)).collect(Collectors.toList());
    }

    private static List<CycleDataDTO> buildComparisonCycles(List<HormoneDTO> hormoneDatas, List<CycleDataDTO> selectedCycles) {
        List<CycleDataDTO> comparisonCycles = new ArrayList<>();
        for (CycleDataDTO cycleDataDTO : selectedCycles) {
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, cycleDataDTO.getLen_cycle(), DatePatternConst.DATE_PATTERN);
            List<HormoneDTO> graphHormoneDatas =
                    hormoneDatas.stream()
                                .filter(hormoneDTO -> LocalDateUtil.minusToDay(LocalDateUtil.dateTime2Date(hormoneDTO.getTest_time()), datePeriodStart) >= 0
                                        && LocalDateUtil.minusToDay(LocalDateUtil.dateTime2Date(hormoneDTO.getTest_time()), dateCycleEnd) < 0)
                                .collect(Collectors.toList());
            if (!graphHormoneDatas.isEmpty()) {
                comparisonCycles.add(cycleDataDTO);
            }
        }
        return comparisonCycles;
    }


    private static List<ReportVO.ValueRange> buildValueRanges(List<ChartLineVO.Legend> legends,
                                                              List<WandTestBiomarkerDTO> filteredWandTestBiomarkerDTOS, List<TemperatureChartDTO> allSelectTemperatureDatas) {
        List<ReportVO.ValueRange> valueRanges = new ArrayList<>();
        for (ChartLineVO.Legend legend : legends) {
            ReportVO.ValueRange valueRange = new ReportVO.ValueRange();
            valueRange.setWandType(legend.getCode());
            List<String> values;
            if (WandTypeEnum.BBT.name().equals(legend.getCode())) {
                values = allSelectTemperatureDatas.stream()
                                                  .map(new Function<TemperatureChartDTO, String>() {
                                                      @Override
                                                      public String apply(TemperatureChartDTO temperatureChartDTO) {
                                                          return temperatureChartDTO.getValue().toString();
                                                      }
                                                  })
                                                  .collect(Collectors.toList());

            } else {
                values = filteredWandTestBiomarkerDTOS.stream()
                                                      .filter(wandTestBiomarkerDTO -> legend.getCode().equals(wandTestBiomarkerDTO.getWandType()))
                                                      .map(WandTestBiomarkerDTO::getTestValue)
                                                      .collect(Collectors.toList());
            }
            Float maxEq = null, maxQt = null, minEq = null, minLt = null;
            for (String v : values) {
                if (v.startsWith("<")) {
                    Float value = Float.valueOf(v.trim().substring(2));
                    if (minLt == null) {
                        minLt = value;
                    } else {
                        minLt = minLt < value ? minLt : value;
                    }
                } else if (v.startsWith(">")) {
                    Float value = Float.valueOf(v.trim().substring(2));
                    if (maxQt == null) {
                        maxQt = value;
                    } else {
                        maxQt = maxQt > value ? maxQt : value;
                    }
                } else {
                    Float value = Float.valueOf(v.trim());
                    if (maxEq == null) {
                        maxEq = value;
                    } else {
                        maxEq = maxEq > value ? maxEq : value;
                    }
                    if (minEq == null) {
                        minEq = value;
                    } else {
                        minEq = minEq < value ? minEq : value;
                    }
                }
            }
            if (maxEq == null) {
                valueRange.setHigh("> " + maxQt + " ");
            } else if (maxQt == null) {
                valueRange.setHigh(maxEq + " ");
            } else if (maxEq > maxQt) {
                valueRange.setHigh(maxEq + " ");
            } else {
                valueRange.setHigh("> " + maxQt + " ");
            }
            if (minEq == null) {
                valueRange.setLow("> " + minLt + " ");
            } else if (minLt == null) {
                valueRange.setLow(minEq + " ");
            } else if (minEq < minLt) {
                valueRange.setLow(minEq + " ");
            } else {
                valueRange.setLow("< " + minLt + " ");
            }
            valueRanges.add(valueRange);
        }
        return valueRanges;
    }

    private ReportVO.CycleCompare buildCycleCompare(List<CycleDataVO> dataInCompareCycles, Map<String, ReportAppWandsMetricDTO> wandsMetricDTOMap) {
        ReportVO.CycleCompare cycleCompare = new ReportVO.CycleCompare();
        int boundaryLimit = 0;
        List<ReportVO.CycleCompare.Cycle> cycleCompareCycles = new ArrayList<>();
        for (CycleDataVO dataInCompareCycle : dataInCompareCycles) {
            ReportVO.CycleCompare.Cycle cycle = new ReportVO.CycleCompare.Cycle();
            String dateOvulation = dataInCompareCycle.getDateOvulation();
            Integer lenCycle = dataInCompareCycle.getLenCycle();
            String datePeriodStart = dataInCompareCycle.getDatePeriodStart();

            if (StringUtils.isBlank(dateOvulation) || StringUtils.isBlank(datePeriodStart)) {
                continue;
            }

            Integer subtract = LocalDateUtil.minusToDay(dateOvulation, datePeriodStart);
            boundaryLimit = Math.max(boundaryLimit, Math.max(subtract, lenCycle - subtract));

            cycle.setCycleIndex(dataInCompareCycle.getCycleIndex());
            long baseTimestamp = LocalDateUtil.getTimestamp(dateOvulation);
            cycle.setLhDatas(transferToCompareDatas(dataInCompareCycle.getLhDatas().stream().map(vo -> (TestDataDTO) vo).collect(Collectors.toList()), baseTimestamp));
            cycle.setE3gDatas(transferToCompareDatas(dataInCompareCycle.getE3gDatas().stream().map(vo -> (TestDataDTO) vo).collect(Collectors.toList()), baseTimestamp));
            cycle.setPdgDatas(transferToCompareDatas(dataInCompareCycle.getPdgDatas().stream().map(vo -> (TestDataDTO) vo).collect(Collectors.toList()), baseTimestamp));
            cycle.setFshDatas(transferToCompareDatas(dataInCompareCycle.getFshDatas().stream().map(vo -> (TestDataDTO) vo).collect(Collectors.toList()), baseTimestamp));

            List<ReportVO.CycleCompare.Cycle.CompareDataDTO> compareBbtDatas = new ArrayList<>();
            for (TemperatureChartDTO testDataDTO : dataInCompareCycle.getTemperatureDatas()) {
                ReportVO.CycleCompare.Cycle.CompareDataDTO compareDataDTO = new ReportVO.CycleCompare.Cycle.CompareDataDTO();
                BeanUtils.copyProperties(testDataDTO, compareDataDTO);
                long testDateTimestamp = LocalDateUtil.getTimestamp(testDataDTO.getTestTime());
                Float index = BigDecimal.valueOf(testDateTimestamp - baseTimestamp).divide(timeStampOneDay, 6, RoundingMode.HALF_DOWN).floatValue();
                compareDataDTO.setIndex(index);
                compareBbtDatas.add(compareDataDTO);
            }
            cycle.setTemperatureDatas(compareBbtDatas);
            cycleCompareCycles.add(cycle);
        }
        cycleCompare.setCycles(cycleCompareCycles);
        cycleCompare.setBoundaryLimit(Math.min(boundaryLimit, 25));
        cycleCompare.setWandsMetricDTOMap(wandsMetricDTOMap);
        return cycleCompare;
    }

    private static List<ReportVO.CycleCompare.Cycle.CompareDataDTO> transferToCompareDatas(List<TestDataDTO> datas, long baseTimestamp) {
        List<ReportVO.CycleCompare.Cycle.CompareDataDTO> compareDatas = new ArrayList<>();
        for (TestDataDTO testDataDTO : datas) {
            ReportVO.CycleCompare.Cycle.CompareDataDTO compareDataDTO = new ReportVO.CycleCompare.Cycle.CompareDataDTO();
            BeanUtils.copyProperties(testDataDTO, compareDataDTO);
            long testDateTimestamp = LocalDateUtil.getTimestamp(testDataDTO.getTestTime());
            Float index = BigDecimal.valueOf(testDateTimestamp - baseTimestamp).divide(timeStampOneDay, 6, RoundingMode.HALF_DOWN).floatValue();
            compareDataDTO.setIndex(index);
            compareDatas.add(compareDataDTO);
        }
        return compareDatas;
    }

    /**
     * @param cycleDataDTOS
     * @param selectedCycles
     * @param today
     * @return
     */
    private static List<CycleDataDTO> buildAnalysisCycles(List<CycleDataDTO> cycleDataDTOS, List<CycleDataDTO> selectedCycles, String today) {
        Integer cycle_index_0 = selectedCycles.get(0).getCycle_index();
        List<CycleDataDTO> analysisCycles = new ArrayList<>();
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            if (!cycleDataDTO.getCycle_status().equals(CycleStatusEnum.REAL_CYCLE.getStatus())
                    && !cycleDataDTO.getCycle_status().equals(CycleStatusEnum.FORECAST_CYCLE.getStatus())) {
                continue;
            }
            if (cycleDataDTO.getCycle_index() < cycle_index_0) {
                continue;
            }
            String endDay = LocalDateUtil.plusDay(cycleDataDTO.getDate_period_start(), cycleDataDTO.getLen_cycle(), DatePatternConst.DATE_PATTERN);
            if (LocalDateUtil.minusToDay(today, endDay) > 0) {
                analysisCycles.add(cycleDataDTO);
            }
        }
        return analysisCycles;
    }

    /**
     * @param cycleSelectTypeEnum
     * @param today
     * @param cycleDataDTOS
     * @param validHormoneDTOS
     * @return
     */
    private static List<CycleDataDTO> buildSelectedCycles(CycleSelectTypeEnum cycleSelectTypeEnum, String today,
                                                          List<CycleDataDTO> cycleDataDTOS, List<HormoneDTO> validHormoneDTOS) {
        //第一次遍历cycles
        List<CycleDataDTO> selectedCycles = buildSecondSelectedCycles(cycleSelectTypeEnum, cycleDataDTOS, validHormoneDTOS, today);
        Integer maxCycleIndex = selectedCycles.get(0).getCycle_index();
        Integer minCycleIndex = selectedCycles.get(selectedCycles.size() - 1).getCycle_index();
        List<Integer> cycleIndexs = selectedCycles.stream().map(CycleDataDTO::getCycle_index).collect(Collectors.toList());
        //再次遍历cycles，将首尾之间过滤掉的cycle加进去
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            if (!cycleDataDTO.getCycle_status().equals(CycleStatusEnum.REAL_CYCLE.getStatus())
                    && !cycleDataDTO.getCycle_status().equals(CycleStatusEnum.FORECAST_CYCLE.getStatus())) {
                continue;
            }
            Integer cycleIndex = cycleDataDTO.getCycle_index();
            if (!cycleIndexs.contains(cycleIndex) && cycleIndex > minCycleIndex && cycleIndex < maxCycleIndex) {
                selectedCycles.add(cycleDataDTO);
            }
        }
        return selectedCycles.stream().sorted(Comparator.comparing(CycleDataDTO::getCycle_index)).collect(Collectors.toList());
    }


    /**
     * step1:第一次遍历cycles，取周期内有有效测试数据的实周期或预测周期
     *
     * @param cycleSelectTypeEnum
     * @param cycleDataDTOS
     * @param validHormoneDTOS
     * @param today
     * @return
     */
    private static List<CycleDataDTO> buildSecondSelectedCycles(CycleSelectTypeEnum cycleSelectTypeEnum, List<CycleDataDTO> cycleDataDTOS,
                                                                List<HormoneDTO> validHormoneDTOS, String today) {
        List<CycleDataDTO> selectedCycles = new ArrayList<>();
        int selectedCycleSize = 0;

        for (int i = cycleDataDTOS.size() - 1; i >= 0; i--) {
            CycleDataDTO cycleDataDTO = cycleDataDTOS.get(i);
            if (!cycleDataDTO.getCycle_status().equals(CycleStatusEnum.REAL_CYCLE.getStatus())
                    && !cycleDataDTO.getCycle_status().equals(CycleStatusEnum.FORECAST_CYCLE.getStatus())) {
                continue;
            }
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, cycleDataDTO.getLen_cycle(), DatePatternConst.DATE_PATTERN);
            List<HormoneDTO> selectHormoneDatas = validHormoneDTOS
                    .stream()
                    .filter(hormoneDTO -> LocalDateUtil.minusToDay(LocalDateUtil.dateTime2Date(hormoneDTO.getTest_time()), datePeriodStart) >= 0
                            && LocalDateUtil.minusToDay(LocalDateUtil.dateTime2Date(hormoneDTO.getTest_time()), dateCycleEnd) < 0)
                    .collect(Collectors.toList());
            if (Objects.requireNonNull(cycleSelectTypeEnum) == CycleSelectTypeEnum.ALL) {
                if (!selectHormoneDatas.isEmpty()) {
                    selectedCycles.add(cycleDataDTO);
                    selectedCycleSize++;
                }
            } else {
                if (!selectHormoneDatas.isEmpty() && selectedCycleSize < cycleSelectTypeEnum.getCode()) {
                    selectedCycles.add(cycleDataDTO);
                    selectedCycleSize++;
                }
            }
        }
        List<Integer> cycleIndexs = selectedCycles.stream().map(CycleDataDTO::getCycle_index).collect(Collectors.toList());
        /**
         * 需要额外将今天所在的周期加进去，且该周期并不计算限制个数（如果没有添加的话）
         */
        for (int i = cycleDataDTOS.size() - 1; i >= 0; i--) {
            CycleDataDTO cycleDataDTO = cycleDataDTOS.get(i);
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, cycleDataDTO.getLen_cycle(), DatePatternConst.DATE_PATTERN);
            if (LocalDateUtil.minusToDay(today, datePeriodStart) >= 0
                    && LocalDateUtil.minusToDay(today, dateCycleEnd) < 0) {
                if (!cycleIndexs.contains(cycleDataDTO.getCycle_index())) {
                    selectedCycles.add(cycleDataDTO);
                }
            }
        }

        return selectedCycles;
    }

    private List<ReportVO.DataRecord> buildDataRecords(Long userId, List<WandTestBiomarkerDTO> filteredWandTestBiomarkerDTOS,
                                                       String startDate, String endDate) {
        List<ReportVO.DataRecord> dataRecords = new ArrayList<>();
        //setPregnant
        setDataRecordsPregnant(userId, startDate, endDate, dataRecords);
        //setMedications
        setDataRecordsMedications(userId, startDate, endDate, dataRecords);
        //setHormones
        List<WandTestBiomarkerDTO> secondFilteredWandTestBiomarkerDTOS = filteredWandTestBiomarkerDTOS
                .stream()
                .filter(wandTestBiomarkerDTO -> LocalDateUtil.minusToDay(LocalDateUtil.dateTime2Date(wandTestBiomarkerDTO.getTestTime()), endDate) <= 0
                        && LocalDateUtil.minusToDay(LocalDateUtil.dateTime2Date(wandTestBiomarkerDTO.getTestTime()), startDate) >= 0)
                .collect(Collectors.toList());

        for (WandTestBiomarkerDTO wandTestBiomarkerDTO : secondFilteredWandTestBiomarkerDTOS) {
            String date = LocalDateUtil.dateTime2Date(wandTestBiomarkerDTO.getTestTime());
            boolean anyMatch = dataRecords.stream().anyMatch(dataRecord -> date.equals(dataRecord.getDate()));
            if (anyMatch) {
                ReportVO.DataRecord dataRecord = dataRecords.stream().filter(dataRecord1 -> date.equals(dataRecord1.getDate())).findFirst().get();
                List<ReportVO.DataRecord.Hormone> hormones = dataRecord.getHormones();
                String wandType = wandTestBiomarkerDTO.getWandType();
                boolean wandTypeMatch = hormones.stream().anyMatch(hormone -> hormone.getBiomarker().equals(wandType));
                if (wandTypeMatch) {
                    ReportVO.DataRecord.Hormone hormone = hormones.stream().filter(hormone1 -> wandType.equals(hormone1.getBiomarker())).findFirst().get();
                    List<ReportVO.DataRecord.Hormone.WandResult> wandResults = hormone.getWandResults();
                    if (CollectionUtils.isEmpty(wandResults)) {
                        wandResults = new ArrayList<>();
                    }
                    ReportVO.DataRecord.Hormone.WandResult wandResult = new ReportVO.DataRecord.Hormone.WandResult();
                    wandResult.setTestTime(wandTestBiomarkerDTO.getTestTime());
                    wandResult.setTestValue(wandTestBiomarkerDTO.getTestValue());
                    wandResults.add(wandResult);
                    hormone.setWandResults(wandResults);
                } else {
                    ReportVO.DataRecord.Hormone hormone = new ReportVO.DataRecord.Hormone();
                    hormone.setBiomarker(wandTestBiomarkerDTO.getWandType());
                    ArrayList<ReportVO.DataRecord.Hormone.WandResult> wandResults = new ArrayList<>();
                    ReportVO.DataRecord.Hormone.WandResult wandResult = new ReportVO.DataRecord.Hormone.WandResult();
                    wandResult.setTestTime(wandTestBiomarkerDTO.getTestTime());
                    wandResult.setTestValue(wandTestBiomarkerDTO.getTestValue());
                    wandResults.add(wandResult);
                    hormone.setWandResults(wandResults);
                    hormones.add(hormone);
                }
            } else {
                ReportVO.DataRecord dataRecord = new ReportVO.DataRecord();
                dataRecord.setDate(date);
                List<ReportVO.DataRecord.Hormone> hormones = new ArrayList<>();
                ReportVO.DataRecord.Hormone hormone = new ReportVO.DataRecord.Hormone();
                hormone.setBiomarker(wandTestBiomarkerDTO.getWandType());
                ArrayList<ReportVO.DataRecord.Hormone.WandResult> wandResults = new ArrayList<>();
                ReportVO.DataRecord.Hormone.WandResult wandResult = new ReportVO.DataRecord.Hormone.WandResult();
                wandResult.setTestTime(wandTestBiomarkerDTO.getTestTime());
                wandResult.setTestValue(wandTestBiomarkerDTO.getTestValue());
                wandResults.add(wandResult);
                hormone.setWandResults(wandResults);
                hormones.add(hormone);
                dataRecord.setHormones(hormones);
                dataRecords.add(dataRecord);
            }
        }
        //setCount
        for (ReportVO.DataRecord dataRecord : dataRecords) {
            int count = Math.max(dataRecord.getMedications().size(), 1);
            int wandResultCount = 0;
            for (ReportVO.DataRecord.Hormone hormone : dataRecord.getHormones()) {
                wandResultCount += hormone.getWandResults().size();
            }
            dataRecord.setCount(Math.max(wandResultCount, count));
        }
        //sort
        return dataRecords.stream().sorted(Comparator.comparing(ReportVO.DataRecord::getDate)).collect(Collectors.toList());
    }

    private void setDataRecordsMedications(Long userId, String startDate, String endDate, List<ReportVO.DataRecord> dataRecords) {
        Map<String, List<UserMedicineDTO>> medicationsMap = new HashMap<>();
        List<AppUserDiaryMedicationsEntity> appUserDiaryMedicationsEntities = appUserDiaryMedicationsDAO.listByUserIdAndBetweenDay(userId, startDate, endDate);
        if (CollectionUtils.isNotEmpty(appUserDiaryMedicationsEntities)) {
            for (AppUserDiaryMedicationsEntity userDiaryMedicationsEntity : appUserDiaryMedicationsEntities) {
                String medications = userDiaryMedicationsEntity.getMedications();
                List<UserMedicineDTO> dailyMedicineDTOS = JsonUtil.toArray(medications, UserMedicineDTO.class);
                if (!dailyMedicineDTOS.isEmpty()) {
                    medicationsMap.put(userDiaryMedicationsEntity.getDiaryDayStr(), dailyMedicineDTOS);
                }
            }
        }

        medicationsMap.forEach((diaryDayStr, dailyMedicineDTOS) -> {
            boolean anyMatch = dataRecords.stream().anyMatch(dataRecord -> diaryDayStr.equals(dataRecord.getDate()));
            if (anyMatch) {
                ReportVO.DataRecord dataRecord = dataRecords.stream().filter(dataRecord1 -> diaryDayStr.equals(dataRecord1.getDate())).findFirst().get();
                dataRecord.setMedications(dailyMedicineDTOS);
            } else {
                ReportVO.DataRecord dataRecord = new ReportVO.DataRecord();
                dataRecord.setDate(diaryDayStr);
                dataRecord.setMedications(dailyMedicineDTOS);
                dataRecords.add(dataRecord);
            }
        });
    }

    private void setDataRecordsPregnant(Long userId, String startDate, String endDate, List<ReportVO.DataRecord> dataRecords) {
        List<AppUserDiaryEntity> appUserDiaryEntities = appUserDiaryDAO.listByUserIdAndBetweenDay(userId, startDate, endDate);
        for (AppUserDiaryEntity appUserDiaryEntity : appUserDiaryEntities) {
            String diaryDayStr = appUserDiaryEntity.getDiaryDayStr();
            boolean anyMatch = dataRecords.stream().anyMatch(dataRecord -> diaryDayStr.equals(dataRecord.getDate()));
            if (anyMatch) {
                ReportVO.DataRecord dataRecord = dataRecords.stream().filter(dataRecord1 -> diaryDayStr.equals(dataRecord1.getDate())).findFirst().get();
                dataRecord.setPregnant(appUserDiaryEntity.getPregnant());
            } else {
                ReportVO.DataRecord dataRecord = new ReportVO.DataRecord();
                dataRecord.setDate(diaryDayStr);
                dataRecord.setPregnant(appUserDiaryEntity.getPregnant());
                dataRecords.add(dataRecord);
            }
        }
    }

    private static void setBaseInfo(ReportVO reportVO, LoginUserInfoDTO loginUserInfoDTO, String startDate, String endDate) {
        ReportVO.BaseInfo baseInfo = new ReportVO.BaseInfo();
        baseInfo.setAvatar(loginUserInfoDTO.getAvatar());
        baseInfo.setFirstName(loginUserInfoDTO.getFirstName());
        baseInfo.setLastName(loginUserInfoDTO.getLastName());
        baseInfo.setAge(AgeUtil.calculateAge(loginUserInfoDTO.getBirthYear(), loginUserInfoDTO.getBirthMonth(), loginUserInfoDTO.getBirthDay()));
        baseInfo.setStartDate(startDate);
        baseInfo.setEndDate(endDate);
        reportVO.setBaseInfo(baseInfo);

        if (loginUserInfoDTO.getTtaSwitch() == 1
                && UserGoalEnum.TTA.getValue().equals(loginUserInfoDTO.getGoalStatus())) {
            reportVO.setUserMode(UserGoalEnum.TTA.getValue());
        } else if (UserGoalEnum.OFT.getValue().equals(loginUserInfoDTO.getGoalStatus())) {
            reportVO.setUserMode(UserGoalEnum.OFT.getValue());
        } else {
            reportVO.setUserMode(UserGoalEnum.TTC.getValue());
        }
    }


    private String buildTemplate(String template, List<ReportSummaryModel> model_data, Integer model_id) {
        //匹配大括号
        String braceRegex = "\\{([^}]*)\\}";
        Matcher matcher = Pattern.compile(braceRegex).matcher(template);
        List<String> matchedStr = new ArrayList<>();
        while (matcher.find()) {
            String group = matcher.group();
            matchedStr.add(group);
        }

        List<String> replaceStr = new ArrayList<>();

        for (ReportSummaryModel modelData : model_data) {
            String dataType = modelData.getData_type();
            String dataValue = modelData.getData_value();
            switch (Objects.requireNonNull(AlgorithmReportSummaryDataTypeEnum.getEnumByCode(dataType))) {
                case Int:
                case FLOAT:
                case STRING:
                    replaceStr.add(dataValue);
                    break;
                case SymptomType:
                    replaceStr.add(DailySymptomEnum.getDescriptionByValue(dataValue));
                    break;
                case SymptomTypeList:
                    replaceStr.add(DailySymptomEnum.getDescriptionsByValues(dataValue));
                    break;
                default:
                    replaceStr.add(" ");
                    break;
            }
        }

        if (matchedStr.size() != replaceStr.size()) {
            throw new UserException("template error:" + model_id + ",template:" + template);
        }

        if (matchedStr.size() == replaceStr.size()) {
            for (int i = 0; i < matchedStr.size(); i++) {
                template = template.replace(matchedStr.get(i), replaceStr.get(i));
            }
        }
        return template;
    }
}
