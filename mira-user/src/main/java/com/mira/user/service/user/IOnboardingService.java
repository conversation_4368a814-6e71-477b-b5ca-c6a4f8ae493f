package com.mira.user.service.user;

import com.mira.api.user.dto.user.diary.UserMedicineDTO;
import com.mira.user.dto.info.UserBirthdayDTO;
import com.mira.user.dto.info.UserCycleLengthDTO;
import com.mira.user.dto.info.UserPeriodLengthDTO;
import com.mira.user.dto.info.v5.MenopauseGoalDTO;
import com.mira.user.dto.info.v5.MenopauseHrtTypeDTO;
import com.mira.user.dto.info.v5.MenopauseSymptomDTO;
import com.mira.user.enums.user.UserConditionEnum;
import com.mira.user.enums.user.onboarding.OnboardingEnum;

import java.util.List;

/**
 * Onboarding service
 *
 * <AUTHOR>
 */
public interface IOnboardingService {
    /**
     * 从哪里知道的Mira
     *
     * @see OnboardingEnum
     * @param options 选项
     */
    void discoverySource(List<Integer> options);

    /**
     * 保存用户info信息中的birthday
     *
     * @param userBirthdayDTO 用户生日
     */
    void saveBirthday(UserBirthdayDTO userBirthdayDTO);

    /**
     * 用户Health Goals
     *
     * @see OnboardingEnum
     * @param options 选项
     * @param update  是否更新
     */
    void healthGoals(List<Integer> options, Integer update);

    /**
     * 用户平均经期长度
     *
     * @param userPeriodLengthDTO 参数
     */
    void averagePeriodLength(UserPeriodLengthDTO userPeriodLengthDTO);

    /**
     * 用户平均经期长度
     *
     * @param userCycleLengthDTO 参数
     */
    void averageCycleLength(UserCycleLengthDTO userCycleLengthDTO);

    /**
     * 用户无经期，选项
     *
     * @see OnboardingEnum
     * @param options 选项
     */
    void noPeriod(List<Integer> options);

    /**
     * 用户最近经期
     *
     * @param dates 经期日期
     */
    void lastPeriod(List<String> dates);

    /**
     * 其他Tracking Tools
     *
     * @see OnboardingEnum
     * @param options 选项
     */
    void otherTrackingTools(List<Integer> options);

    /**
     * 其他Considerations
     *
     * @see UserConditionEnum
     * @param options 选项
     */
    void otherConsiderations(List<Integer> options);

    /**
     * 医疗过程
     *
     * @see OnboardingEnum
     * @param options 选项
     */
    void medicalProcedures(List<Integer> options);

    /**
     * 是否服用药物或补充剂
     *
     * @param option 选项
     */
    void medicationsOption(Integer option);

    /**
     * 保存药物或补充剂
     *
     * @param medications 药物信息
     */
    void medications(List<UserMedicineDTO> medications);

    /**
     * 怀孕历史
     *
     * @param option 选项
     */
    void pregnancyHistory(Integer option);

    /**
     * 怀孕计划
     *
     * @param options 选项
     */
    void pregnancyPlan(List<Integer> options);

    // -------------------------------- Menopause Info --------------------------------

    /**
     * Monther Onset Age
     *
     * @param age    年龄
     * @param update 是否更新
     */
    void menopauseMontherOnsetAge(Integer age, Integer update);

    /**
     * HRT select
     *
     * @param option 0-否，1-是
     * @param update
     */
    void menopauseHrt(Integer option, Integer update);

    /**
     * HRT Status
     *
     * @param menopauseHrtTypeDTO 参数
     * @param update              是否更新
     */
    void menopauseHrtStatus(MenopauseHrtTypeDTO menopauseHrtTypeDTO, Integer update);

    /**
     * Your Goals
     *
     * @param menopauseGoalDTO 参数
     * @param update           是否更新
     */
    void menopauseYourGoals(MenopauseGoalDTO menopauseGoalDTO, Integer update);

    /**
     * Symptoms
     *
     * @param menopauseSymptoms 参数
     * @param update            是否更新
     */
    void menopauseSymptoms(List<MenopauseSymptomDTO> menopauseSymptoms, Integer update);

    // -------------------------------- Life Style --------------------------------

    /**
     * Understanding your stress
     *
     * @param option 选项
     */
    void lifestyleStressLevel(Integer option);

    /**
     * Physical activity
     *
     * @param options 选项
     */
    void lifestylePhysicalActivity(List<Integer> options);

    /**
     * Activity level
     *
     * @param option 选项
     */
    void lifestyleActivityLevel(Integer option);

    /**
     * Dietary habits
     *
     * @param option 选项
     */
    void lifestyleDietaryHabits(Integer option);
}
