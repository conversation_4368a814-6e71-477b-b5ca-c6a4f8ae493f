package com.mira.user.controller.vo.report;

import com.mira.api.bluetooth.dto.cycle.TestDataDTO;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.user.dto.user.diary.UserMedicineDTO;
import com.mira.api.user.enums.daily.DailyStatusPregnantEnum;
import com.mira.user.controller.vo.chart.ChartLineVO;
import com.mira.user.controller.vo.chart.PregnantRiskVO;
import com.mira.user.controller.vo.cycle.CycleAnalysisVO;
import com.mira.user.controller.vo.cycle.TestDataVO;
import com.mira.user.dto.chart.TemperatureChartDTO;
import com.mira.api.user.dto.user.diary.DateSymptomDTO;
import com.mira.user.dto.report.ReportAppWandsMetricDTO;
import com.mira.user.dto.report.ReportAppWandsMetricResultDTO;
import com.mira.user.enums.report.ReportDataShapeEnum;
import com.mira.user.enums.user.UserConditionEnum;
import com.mira.user.enums.user.v5.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2022-11-14
 **/
@Data
public class ReportVO {
    @ApiModelProperty(value = "基本信息")
    private BaseInfo baseInfo;

    @ApiModelProperty(value = "用户的Mode，参考UserGoalEnum")
    private Integer userMode;

    @ApiModelProperty("是否跟踪更年期，null无标识;0 不跟踪;1跟踪")
    private Integer trackingMenopause;

    @ApiModelProperty(" menopause定义Stage的种类:\n" +
            "    null或者0:无标识\n" +
            "    1:'Late reproductive age'\n" +
            "    2:'Early menopause transition'\n" +
            "    3:'Late menopause transition'\n" +
            "    4:'Menopause'\n" +
            "    5:'Early menopause'\n" +
            "    6:'remature menopause';7:Post menopause;8:Undefined;" +
            "101~108:Stage1: Late reproductive age ~ Stage1: Undefined")
    private Integer menopauseStage;

    @ApiModelProperty("menopause定义Stage的date,\"2024-07-12\"")
    private String defineStageDate;

    @ApiModelProperty(value = "更年期survey结果信息")
    private MenopauseSurveyInfo menopauseSurveyInfo;

    @ApiModelProperty(value = "曲线类型：LH、HCH、E3G等")
    private List<ChartLineVO.Legend> legend;

    @ApiModelProperty(value = "温度默认单位，默认值与国际化对应")
    private String tempUnit;

    @ApiModelProperty(value = "一般总结")
    private List<String> generalSummary;

    @ApiModelProperty(value = "周期分析结果")
    private CycleAnalysisVO cycleAnalysisVO;

    @ApiModelProperty(value = "单独周期分析")
    private List<Cycle> cycles;

    @ApiModelProperty(value = "症状汇总")
    private List<DateSymptomDTO> dateSymptomDTOS;

    @ApiModelProperty(value = "周期比对数据")
    private CycleCompare cycleCompare;

    @ApiModelProperty(value = "周期比对点的形状")
    private List<CompareShape> compareShapes;

    @ApiModelProperty(value = "数值变化汇总")
    private List<ValueRange> valueRanges;

    @ApiModelProperty(value = "测试数据记录")
    private List<DataRecord> dataRecords;

    @ApiModelProperty(value = "阴影渲染:100 cycle length; 101 follicular length;102 luteal length")
    private ReportAppWandsMetricResultDTO reportAppWandsMetricResultDTO;

    @ApiModelProperty("无经期模式，0-否，1-是")
    private Integer noPeriod = 0;

    @Data
    @ApiModel("周期比对点的形状")
    public static class CompareShape {
        @ApiModelProperty(value = "形状标识")
        private Integer shapeIndex;

        /**
         * @see ReportDataShapeEnum
         */
        @ApiModelProperty(value = "周期主键")
        private Integer cycleIndex;

        @ApiModelProperty(value = "周期开始日")
        private String datePeriodStart;

        @ApiModelProperty(value = "周期结束日")
        private String dateCycleEnd;
    }

    @Data
    @ApiModel("基本信息")
    public static class BaseInfo {
        @ApiModelProperty(value = "英文名")
        private String firstName;

        @ApiModelProperty(value = "英文姓")
        private String lastName;

        @ApiModelProperty(value = "头像")
        private String avatar;

        @ApiModelProperty(value = "用户年龄")
        private Integer age;

        @ApiModelProperty(value = "报告开始时间")
        private String startDate;

        @ApiModelProperty(value = "报告结束时间")
        private String endDate;
    }

    @Data
    @ApiModel("数值变化")
    public static class ValueRange {
        @ApiModelProperty(value = "试剂类型")
        private String wandType;

        @ApiModelProperty(value = "高值")
        private String high;

        @ApiModelProperty(value = "低值")
        private String low;
    }

    @Data
    @ApiModel("周期数据")
    public static class Cycle {
        @ApiModelProperty(value = "主键")
        private Integer cycleIndex;

        /**
         * @see CycleStatusEnum
         */
        @ApiModelProperty(value = "周期状态")
        private Integer cycleStatus;

        @ApiModelProperty(value = "周期长度")
        private Integer lenCycle;

        @ApiModelProperty(value = "周期开始日")
        private String datePeriodStart;

        @ApiModelProperty(value = "经期结束日（经期不包含这天")
        private String datePeriodEnd;

        @ApiModelProperty(value = "易孕期开始日")
        private String dateFwStart;

        @ApiModelProperty(value = "易孕期结束日 （易孕期不包含这一天）")
        private String dateFwEnd;

        @ApiModelProperty(value = "排卵日 （预测 or LH 峰值日")
        private String dateOvulation;

        @ApiModelProperty(value = "预留，可为 null （实际测量的最高值时间）")
        private String dateLhSurge;

        @ApiModelProperty(value = "float, 实际峰值日 中 LH的对应最大值，可为 null")
        private Float valueLhSurge;

        @ApiModelProperty(value = "float, LH 阈值,可为 null")
        private Float thresholdLh;

        @ApiModelProperty(value = "float, E3G 阈值,可为 null")
        private Float thresholdE3g;

        @ApiModelProperty(value = "float, pdG 动态均值,可为 null")
        private Float pdgDynamic;

        @ApiModelProperty("周期cd状态（当前chart页面仅在怀孕周期8和异常怀孕周期9返回）")
        private List<Map<String, String>> weeks;

        @ApiModelProperty(value = "怀孕风险预测")
        private PregnantRiskVO pregnantRisk;

        @ApiModelProperty(value = "lh测试数据")
        private List<TestDataVO> lhDatas = new ArrayList<>();

        @ApiModelProperty(value = "e3g测试数据")
        private List<TestDataVO> e3gDatas = new ArrayList<>();

        @ApiModelProperty(value = "hcg测试数据")
        private List<TestDataVO> hcgDatas = new ArrayList<>();

        @ApiModelProperty(value = "pdg测试数据")
        private List<TestDataVO> pdgDatas = new ArrayList<>();

        @ApiModelProperty(value = "hcg2测试数据")
        private List<TestDataVO> hcg2Datas = new ArrayList<>();

        @ApiModelProperty(value = "fsh测试数据")
        private List<TestDataVO> fshDatas = new ArrayList<>();

        @ApiModelProperty(value = "温度测试数据")
        private List<TemperatureChartDTO> temperatureDatas = new ArrayList<>();
    }

    @Data
    @ApiModel("周期比对数据")
    public static class CycleCompare {
        /**
         * 边界限制取排卵日距离经期开始日和结束日的距离的最大值与｜25｜的比较
         */
        @ApiModelProperty(value = "边界限制")
        private Integer boundaryLimit;

        @ApiModelProperty(value = "周期数据")
        private List<Cycle> cycles;

        @ApiModelProperty(value = "阴影渲染:各种Biomarker的公差算法结果。Biomarker类型：1:LH,3:E3G,2:HCG,9:PDG,16:FSH")
        private Map<String, ReportAppWandsMetricDTO> wandsMetricDTOMap;

        @Data
        @ApiModel("比对周期")
        public static class Cycle {
            @ApiModelProperty(value = "主键")
            private Integer cycleIndex;

            @ApiModelProperty(value = "lh测试数据")
            private List<CompareDataDTO> lhDatas = new ArrayList<>();

            @ApiModelProperty(value = "e3g测试数据")
            private List<CompareDataDTO> e3gDatas = new ArrayList<>();

            @ApiModelProperty(value = "pdg测试数据")
            private List<CompareDataDTO> pdgDatas = new ArrayList<>();

            @ApiModelProperty(value = "fsh测试数据")
            private List<CompareDataDTO> fshDatas = new ArrayList<>();

            @ApiModelProperty(value = "温度测试数据")
            private List<CompareDataDTO> temperatureDatas = new ArrayList<>();

            @Data
            @ApiModel("数据")
            public static class CompareDataDTO {
                @ApiModelProperty(value = "相对于所在周期的排卵日的位置")
                private Float index;

                @ApiModelProperty(value = "日期")
                private String testTime;

                @ApiModelProperty(value = "测试数据")
                private Float value;
            }
        }
    }

    @Data
    @ApiModel("测试数据记录")
    public static class DataRecord {
        @ApiModelProperty(value = "时间")
        String date;

        @ApiModelProperty(value = "该天总数")
        Integer count;

        /**
         * 验孕记录
         *
         * @see DailyStatusPregnantEnum
         */
        @ApiModelProperty(value = "验孕记录")
        private Boolean pregnant;

        @ApiModelProperty(value = "药物")
        private List<UserMedicineDTO> medications = new ArrayList<>();

        @ApiModelProperty(value = "当天测试数据集合")
        private List<Hormone> hormones = new ArrayList<>();

        @Data
        @ApiModel("测试数据")
        public static class Hormone {
            @ApiModelProperty(value = "Biomarker类型，参考WandTypeEnum")
            private String biomarker;

            @ApiModelProperty(value = "该类型测试结果")
            private List<WandResult> wandResults;

            @Data
            @ApiModel("测试结果")
            public static class WandResult {
                @ApiModelProperty(value = "hormone 测试结果数据时间戳")
                private String testTime;

                @ApiModelProperty(value = "测量值")
                private String testValue;
            }
        }
    }

    @Data
    @ApiModel("更年期survey结果信息")
    public static class MenopauseSurveyInfo {
        /**
         * Have you ever been pregnant?
         *
         * @see MenopausePregnantEnum
         */
        private Integer everPregnant;
        /**
         * @see UserConditionEnum
         */
        @ApiModelProperty("用户 Condition")
        private List<Integer> conditions = new ArrayList<>();
        /**
         * 是否使用药物或者其他支持
         */
        private Integer takeMedication;
        /**
         * 同custom log中的药物
         */
        private List<UserMedicineDTO> medications;
        /**
         * 手术
         *
         * @see MenopauseProcedureEnum
         */
        @ApiModelProperty("procedures")
        private List<Integer> procedures;
        /**
         * (Hormone Replacement Therapy) yes/no
         */
        private Integer hrt;
        /**
         * What kind of HRT you are on
         *
         * @see MenopauseHrtTypeEnum
         */
        private List<Integer> hrtTypes;
        /**
         * @see MenopausePhysicallyActiveEnum
         */
        private Integer physicallyActive;
        /**
         * What type of physical activity
         *
         * @see MenopausePhysicallyActiveTypeEnum
         */
        private List<Integer> physicallyActiveTypes;
    }
}
