package com.mira.clinic.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.CryptoException;
import com.mira.api.clinic.dto.ApplyPatientDTO;
import com.mira.api.clinic.enums.ClinicianStatusEnum;
import com.mira.api.clinic.enums.ClinicPatientStatusEnum;
import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.api.user.consts.UserSourceConst;
import com.mira.api.user.consts.UserStatusConst;
import com.mira.api.user.dto.user.AppPartnerDTO;
import com.mira.api.user.dto.user.AppUserDTO;
import com.mira.api.user.dto.user.AppUserSaveDTO;
import com.mira.api.user.provider.IUserProvider;
import com.mira.clinic.async.AmplitudeProducer;
import com.mira.clinic.async.EmailProducer;
import com.mira.clinic.async.KlaviyoProducer;
import com.mira.clinic.controller.dto.InitDoctorDTO;
import com.mira.clinic.controller.dto.InviteDoctorDTO;
import com.mira.clinic.controller.dto.InvitePatientDTO;
import com.mira.clinic.dal.dao.*;
import com.mira.clinic.dal.entity.*;
import com.mira.clinic.dto.TenantInfoDTO;
import com.mira.api.clinic.dto.ClinicPatientListDTO;
import com.mira.clinic.enums.ClinicDoctorTypeEnum;
import com.mira.clinic.enums.ClinicRoleEnum;
import com.mira.clinic.exception.ClinicException;
import com.mira.clinic.properties.CacheExpireProperties;
import com.mira.clinic.service.IInviteService;
import com.mira.clinic.service.manager.TenantInfoManager;
import com.mira.clinic.service.util.InitPatientNumberUtil;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.RequestSendFlag;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.PasswordUtil;
import com.mira.core.util.RsaUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.web.properties.RsaProperties;
import com.mira.web.util.HashDecodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 邀请医生/护士/病人接口实现
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
@Service
@Slf4j
public class InviteServiceImpl implements IInviteService {
    @Resource
    private AppTenantDoctorDAO appTenantDoctorDAO;

    @Resource
    private AppTenantPatientDAO appTenantPatientDAO;
    @Resource
    private AppTenantDoctorPatientDAO appTenantDoctorPatientDAO;
    @Resource
    private AppTenantDAO appTenantDAO;

    @Resource
    private RsaProperties rsaProperties;
    @Resource
    private CacheExpireProperties cacheExpireProperties;

    @Resource
    private TenantInfoManager tenantInfoManager;

    @Resource
    private EmailProducer emailProducer;
    @Resource
    private AmplitudeProducer amplitudeProducer;
    @Resource
    private KlaviyoProducer klaviyoProducer;

    @Resource
    private IUserProvider userProvider;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void inviteDoctor(InviteDoctorDTO inviteDoctorDTO, int sendFlag) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(appTenantDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String tenantCode = appTenantDoctor.getTenantCode();

        AppTenantDoctorEntity tenantDoctorEntity = appTenantDoctorDAO.getByEmail(inviteDoctorDTO.getEmail());
        AppTenantEntity appTenant = appTenantDAO.getByTenantCode(tenantCode);

        //数据库需要保存是否过期这个状态，列表中当用户状态为邀请过期时，展示resend按钮
        //实现方案：邀请和再次邀请时，保存当前邀请时间。mira desk查询列表时，将状态为邀请中，但是邀请时间已过期的用户的状态改为邀请过期。
        // 但是再次邀请时，需要重置为邀请中，并且刷新邀请时间。
        //再次邀请能逻辑触发的前提是状态为邀请过期

        if (RequestSendFlag.FIRST_SEND == sendFlag) {
            if (tenantDoctorEntity != null) {
                throw new ClinicException(BizCodeEnum.USER_EXIST);
            }
            // 创建账号
            tenantDoctorEntity = new AppTenantDoctorEntity();
            tenantDoctorEntity.setTenantCode(tenantCode);
            tenantDoctorEntity.setEmail(inviteDoctorDTO.getEmail());
            tenantDoctorEntity.setName(inviteDoctorDTO.getName());
            tenantDoctorEntity.setRole(ClinicRoleEnum.DOCTOR.getCode());
            tenantDoctorEntity.setDoctorType(ClinicDoctorTypeEnum.CLINICIAN.getCode());
            tenantDoctorEntity.setPassword("123");
            tenantDoctorEntity.setSalt("123");
            tenantDoctorEntity.setStatus(ClinicianStatusEnum.INVITING.getCode());
            tenantDoctorEntity.setInviteTime(System.currentTimeMillis());
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, tenantDoctorEntity);
            tenantDoctorEntity.setCreator(id);
            tenantDoctorEntity.setModifier(id);
            appTenantDoctorDAO.save(tenantDoctorEntity);
            // 发送邮件
            emailProducer.inviteDoctor(tenantDoctorEntity, appTenant, sendFlag);
        } else if (RequestSendFlag.RESEND == sendFlag) {
            if (tenantDoctorEntity == null) {
                throw new ClinicException(BizCodeEnum.INTERNAL_SERVER_ERROR);
            }
            if (tenantDoctorEntity.getStatus() == ClinicianStatusEnum.INVITATION_EXPIRED.getCode()) {
                tenantDoctorEntity.setStatus(ClinicianStatusEnum.INVITING.getCode());
                tenantDoctorEntity.setInviteTime(System.currentTimeMillis());
                UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, tenantDoctorEntity);
                tenantDoctorEntity.setModifier(id);
                appTenantDoctorDAO.updateById(tenantDoctorEntity);
                // 发送邮件
                emailProducer.inviteDoctor(tenantDoctorEntity, appTenant, sendFlag);
            }
        }

    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void initDoctor(InitDoctorDTO initDoctorDTO) {
        String hash = HashDecodeUtil.decodeHash(initDoctorDTO.getHash());
        String decode;
        try {
            decode = RsaUtil.decodeRsa(hash, rsaProperties.getPrivateKey());
        } catch (CryptoException e) {
            throw new ClinicException(BizCodeEnum.LINK_HAS_EXPIRED);
        }
        Map<String, Object> paramMap = JsonUtil.toObject(decode, HashMap.class);

        Object idValue = paramMap.get("doctorId");
        Long doctorId;
        if (idValue instanceof Integer) {
            doctorId = ((Integer) idValue).longValue();
        } else {
            doctorId = (Long) idValue;
        }

        Long doctorInviteTimestamp = (Long) paramMap.get("doctorInviteTimestamp");
        if (doctorInviteTimestamp + cacheExpireProperties.getInvite() * 60 * 1000 < System.currentTimeMillis()) {
            throw new ClinicException(BizCodeEnum.LINK_HAS_EXPIRED);
        }

        AppTenantDoctorEntity tenantDoctorEntity = appTenantDoctorDAO.getById(doctorId);
        if (tenantDoctorEntity.getStatus() == ClinicianStatusEnum.CONFIRMED.getCode()) {
            throw new ClinicException(BizCodeEnum.USER_EXIST);
        }

        tenantDoctorEntity.setSalt(PasswordUtil.generateSalt(20));
        tenantDoctorEntity.setPassword(PasswordUtil.encryptPassword(initDoctorDTO.getPw(), tenantDoctorEntity.getSalt()));
        tenantDoctorEntity.setStatus(ClinicianStatusEnum.CONFIRMED.getCode());
        UpdateEntityTimeUtil.updateBaseEntityTime(tenantDoctorEntity.getTimeZone(), tenantDoctorEntity);
        appTenantDoctorDAO.updateById(tenantDoctorEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void invitePatient(InvitePatientDTO invitePatientDTO, int sendFlag) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String tenantCode = appTenantDoctor.getTenantCode();

        String firstName = invitePatientDTO.getFirstName();
        String lastName = invitePatientDTO.getLastName();
        String email = invitePatientDTO.getEmail();
        String patientNumber = invitePatientDTO.getPatientNumber();
        List<Long> doctorIds = invitePatientDTO.getDoctorIds();
        TenantInfoDTO tenantInfoDTO = tenantInfoManager.getTenantInfo(id);

        AppPartnerDTO appPartnerDTO = userProvider.getPartnerByEmail(email).getData();
        if (appPartnerDTO != null) {
            // 存在partner，直接报错返回
            throw new ClinicException(BizCodeEnum.PARTNER_EXIST);

        }

        if (RequestSendFlag.FIRST_SEND == sendFlag) {
            // 病人查重,（查询一个tenant下所有病人）涉及到三张表：app_tenant_patient、app_user、app_user_period
            List<ClinicPatientListDTO> clinicPatientListDTOS = appTenantPatientDAO.listTenantPatientByClinic(tenantCode);
            buildPatientStatus(clinicPatientListDTOS);
            if (!clinicPatientListDTOS.isEmpty()) {
                List<String> existEmails = clinicPatientListDTOS.stream()
                        .map(ClinicPatientListDTO::getEmail)
                        .collect(Collectors.toList());
                if (existEmails.contains(email)) {
                    throw new ClinicException(BizCodeEnum.USER_EXIST);
                }
            }

            AppUserDTO appUserDTO = userProvider.getUserByEmail(email).getData();
            if (appUserDTO != null) {
                // 存在user，创建patient，创建patient与doctor关联关系，发email1与notification获取与user绑定的permission
                Long patientId = createPatient(invitePatientDTO, appUserDTO, id, tenantCode);
                createDoctorPatients(id, tenantCode, doctorIds, patientId);
                // 发送邮件
                emailProducer.invitePatient(EmailTypeEnum.INVITE_PATIENT_WITH_USER_EMAIL,
                        tenantInfoDTO, invitePatientDTO, appUserDTO.getId(), patientId);

            } else {
                // 不存在user与partner，创建patient，创建patient与doctor关联关系，发email获取与user绑定的permission
                Long patientId = createPatient(invitePatientDTO, appUserDTO, id, tenantCode);
                createDoctorPatients(id, tenantCode, doctorIds, patientId);
                // 发送邮件
                emailProducer.invitePatient(EmailTypeEnum.INVITE_PATIENT_WITHOUT_USER_EMAIL,
                        tenantInfoDTO, invitePatientDTO, null, patientId);
            }
        } else if (RequestSendFlag.RESEND == sendFlag) {
            AppTenantPatientEntity appTenantPatientEntity = appTenantPatientDAO.getByEmailAndTenantCode(email, tenantInfoDTO.getTenantCode());
            if (ClinicPatientStatusEnum.INVITATION_EXPIRED.getCode().equals(appTenantPatientEntity.getStatus())) {
                appTenantPatientEntity.setStatus(ClinicPatientStatusEnum.INVITING.getCode());
                appTenantPatientEntity.setInviteTime(System.currentTimeMillis());
                UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, appTenantPatientEntity);
                appTenantPatientEntity.setModifier(id);
                appTenantPatientDAO.updateById(appTenantPatientEntity);

                AppUserDTO appUserDTO = userProvider.getUserByEmail(email).getData();
                invitePatientDTO.setFirstName(appTenantPatientEntity.getFirstName());
                invitePatientDTO.setLastName(appTenantPatientEntity.getLastName());
                if (appUserDTO != null) {
                    // 发送邮件
                    emailProducer.invitePatient(EmailTypeEnum.INVITE_PATIENT_WITH_USER_EMAIL,
                            tenantInfoDTO, invitePatientDTO, appUserDTO.getId(), appTenantPatientEntity.getId());

                } else {
                    // 发送邮件
                    emailProducer.invitePatient(EmailTypeEnum.INVITE_PATIENT_WITHOUT_USER_EMAIL,
                            tenantInfoDTO, invitePatientDTO, null, appTenantPatientEntity.getId());
                }
            }
        }

    }

    private void buildPatientStatus(List<ClinicPatientListDTO> clinicPatientListDTOS) {
        for (ClinicPatientListDTO clinicPatientListDTO : clinicPatientListDTOS) {
            Integer userStatus = clinicPatientListDTO.getUserStatus();
            Integer userDeleted = clinicPatientListDTO.getUserDeleted();
            Integer fresh = clinicPatientListDTO.getFresh();
            Long userId = clinicPatientListDTO.getUserId();
            if (userId == null) {
                clinicPatientListDTO.setPatientStatus(ClinicPatientStatusEnum.INVITING.getCode());
            } else if (1 == userDeleted) {
                clinicPatientListDTO.setPatientStatus(ClinicPatientStatusEnum.DELETED.getCode());
                clinicPatientListDTO.setEmail("Deleted");
            } else if (fresh == null && userStatus == 1) {
                clinicPatientListDTO.setPatientStatus(ClinicPatientStatusEnum.REGISTERING.getCode());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Long createPatient(InvitePatientDTO invitePatientDTO, AppUserDTO appUserDTO, Long id, String tenantCode) {
        AppTenantPatientEntity appTenantPatientEntity = new AppTenantPatientEntity();
        appTenantPatientEntity.setTenantCode(tenantCode);
        appTenantPatientEntity.setUserId(Objects.isNull(appUserDTO) ? null : appUserDTO.getId());
        appTenantPatientEntity.setInitEmail(invitePatientDTO.getEmail());
        appTenantPatientEntity.setFirstName(invitePatientDTO.getFirstName());
        appTenantPatientEntity.setLastName(invitePatientDTO.getLastName());
        appTenantPatientEntity.setNickname(invitePatientDTO.getFirstName() + " " + invitePatientDTO.getLastName());
        if (StringUtils.isBlank(invitePatientDTO.getPatientNumber())) {
            Long existCount = appTenantPatientDAO.countTenantPatientByTenantCode(tenantCode);
            appTenantPatientEntity.setPatientNumber(InitPatientNumberUtil.generatePatientNumber(existCount));
        }else {
            appTenantPatientEntity.setPatientNumber(invitePatientDTO.getPatientNumber());
        }
        // 病人账号状态:1:邀请中;2:正常激活状态;0:关闭
        appTenantPatientEntity.setStatus(ClinicPatientStatusEnum.INVITING.getCode());
        appTenantPatientEntity.setInviteTime(System.currentTimeMillis());
        appTenantPatientEntity.setCreator(id);
        appTenantPatientEntity.setModifier(id);
        UpdateEntityTimeUtil.setBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), appTenantPatientEntity);
        appTenantPatientDAO.save(appTenantPatientEntity);
        return appTenantPatientEntity.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void createDoctorPatients(Long id, String tenantCode, List<Long> doctorIds, Long patientId) {
        ArrayList<AppTenantDoctorPatientEntity> tenantDoctorPatientEntities = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(doctorIds)) {
            for (Long doctorId : doctorIds) {
                tenantDoctorPatientEntities.add(buildDoctorPatientRelation(tenantCode, doctorId, patientId, id));
            }
        }
        if (CollectionUtils.isNotEmpty(tenantDoctorPatientEntities)) {
            appTenantDoctorPatientDAO.saveBatch(tenantDoctorPatientEntities);
        }
    }

    private AppTenantDoctorPatientEntity buildDoctorPatientRelation(String tenantCode, Long doctorId,
                                                                    Long patientId, Long id) {
        AppTenantDoctorPatientEntity appTenantDoctorPatientEntity = new AppTenantDoctorPatientEntity();
        appTenantDoctorPatientEntity.setTenantCode(tenantCode);
        appTenantDoctorPatientEntity.setDoctorId(doctorId);
        appTenantDoctorPatientEntity.setPatientId(patientId);
        appTenantDoctorPatientEntity.setCreator(id);
        appTenantDoctorPatientEntity.setModifier(id);
        UpdateEntityTimeUtil.setBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), appTenantDoctorPatientEntity);
        return appTenantDoctorPatientEntity;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void grantPatient(String hash) {
        hash = HashDecodeUtil.decodeHash(hash);
        String decode;
        try {
            decode = RsaUtil.decodeRsa(hash, rsaProperties.getPrivateKey());
        } catch (CryptoException e) {
            throw new ClinicException(BizCodeEnum.LINK_HAS_EXPIRED);
        }
        Map<String, Object> paramMap = JsonUtil.toObject(decode, HashMap.class);
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        Long patientId = Long.valueOf(paramMap.get("patientId").toString());
        Long userId = null;
        if (paramMap.get("userId") != null) {
            userId = Long.valueOf(paramMap.get("userId").toString());
        }
        String email = (String) paramMap.get("email");
        String salt = (String) paramMap.get("salt");
        String password = (String) paramMap.get("password");
        Long patientInviteTimestamp = Long.valueOf(paramMap.get("patientInviteTimestamp").toString());

        if (patientInviteTimestamp + cacheExpireProperties.getInvite() * 60 * 1000 < System.currentTimeMillis()) {
            throw new ClinicException(BizCodeEnum.LINK_HAS_EXPIRED);
        }
        //todo 同一个email可以绑定其他clinic
        // 校验是否已经存在绑定的诊所
        //        AppTenantPatientEntity entityByInitEmail = appTenantPatientDAO.getByEmailAndStatus(email, ClinicStatusEnum.NORMAL.getCode());
        //        if (entityByInitEmail != null) {
        //            AppTenantEntity tenant = appTenantDAO.getByTenantCode(entityByInitEmail.getTenantCode());
        //            throw new ClinicException("Currently bound to clinic: " + tenant.getName());
        //        }

        AppTenantPatientEntity patientEntity = appTenantPatientDAO.getById(patientId);
        if (patientEntity.getStatus() == ClinicPatientStatusEnum.NORMAL.getCode()) {
            throw new ClinicException(BizCodeEnum.USER_EXIST);
        }
        if (userId == null) {
            // 创建user(注册用户)，初始化user的密码,创建userInfo
            AppUserSaveDTO appUserSaveDTO = new AppUserSaveDTO();
            appUserSaveDTO.setName(email);
            appUserSaveDTO.setEmail(email);
            appUserSaveDTO.setSalt(salt);
            appUserSaveDTO.setPassword(password);
            appUserSaveDTO.setPasswordGrade(1);
            appUserSaveDTO.setStatus(UserStatusConst.NORMAL);
            appUserSaveDTO.setSource(UserSourceConst.FOUR);
            appUserSaveDTO.setFirstName(patientEntity.getFirstName());
            appUserSaveDTO.setLastName(patientEntity.getLastName());
            String firstName = patientEntity.getFirstName();
            String lastName = patientEntity.getLastName();
            String nickName = (StringUtils.isBlank(firstName) ? "" : firstName) + " " + (StringUtils.isBlank(lastName) ? "" : lastName);
            appUserSaveDTO.setNickname(nickName);
            AppUserDTO resultAppUser = userProvider.saveUser(appUserSaveDTO).getData();

            // 设置userId与patient绑定
            patientEntity.setStatus(ClinicPatientStatusEnum.NORMAL.getCode());
            patientEntity.setUserId(resultAppUser.getId());
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, patientEntity);
            appTenantPatientDAO.updateById(patientEntity);

        } else {
            // 设置userId与patient绑定
            patientEntity.setUserId(userId);
            patientEntity.setStatus(ClinicPatientStatusEnum.NORMAL.getCode());
            UpdateEntityTimeUtil.updateBaseEntityTime(patientEntity.getTimeZone(), patientEntity);
            appTenantPatientDAO.updateById(patientEntity);
        }
    }

    /**
     * 批量创建病人（仅开发人员使用）
     */
    @Override
    public void batchAddPatient() {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String tenantCode = appTenantDoctor.getTenantCode();

        String pref = "EH";
        for (int i = 1; i <= 300; i++) {
            if (i < 10) {
                pref = "EH00";
            } else if (i < 100) {
                pref = "EH0";
            }
            String email = pref + i + "@mail.com";
            String patientNumber = pref + i;
            String firstName = pref + i;
            String lastName = "TEST";
            String password = RandomUtil.randomString(5) + "@WSX" + i;

            log.info("create account:【{}】 password:【{}】", email, password);

            // 创建user(注册用户)，初始化user的密码,创建userInfo
            AppUserSaveDTO appUserSaveDTO = new AppUserSaveDTO();
            appUserSaveDTO.setName(email);
            appUserSaveDTO.setEmail(email);
            String salt = PasswordUtil.generateSalt(20);
            appUserSaveDTO.setSalt(salt);
            appUserSaveDTO.setPassword(PasswordUtil.encryptPassword(password, salt));
            appUserSaveDTO.setPasswordGrade(10);
            appUserSaveDTO.setStatus(UserStatusConst.NORMAL);
            appUserSaveDTO.setSource(UserSourceConst.FOUR);
            appUserSaveDTO.setFirstName(firstName);
            appUserSaveDTO.setLastName(lastName);
            String nickName = (StringUtils.isBlank(firstName) ? "" : firstName) + " " + (StringUtils.isBlank(lastName) ? "" : lastName);
            appUserSaveDTO.setNickname(nickName);
            AppUserDTO resultAppUser = userProvider.saveUser(appUserSaveDTO).getData();

            InvitePatientDTO invitePatientDTO = new InvitePatientDTO();
            invitePatientDTO.setPatientNumber(patientNumber);
            invitePatientDTO.setEmail(email);
            invitePatientDTO.setFirstName(firstName);
            invitePatientDTO.setLastName(lastName);
            createPatientForBatch(invitePatientDTO, resultAppUser.getId(), id, tenantCode);
        }

    }

    private void createPatientForBatch(InvitePatientDTO invitePatientDTO, Long userId, Long id,
                                       String tenantCode) {
        AppTenantPatientEntity appTenantPatientEntity = new AppTenantPatientEntity();
        appTenantPatientEntity.setTenantCode(tenantCode);
        appTenantPatientEntity.setUserId(userId);
        appTenantPatientEntity.setInitEmail(invitePatientDTO.getEmail());
        appTenantPatientEntity.setFirstName(invitePatientDTO.getFirstName());
        appTenantPatientEntity.setLastName(invitePatientDTO.getLastName());
        appTenantPatientEntity.setNickname(invitePatientDTO.getFirstName() + " " + invitePatientDTO.getLastName());
        appTenantPatientEntity.setPatientNumber(invitePatientDTO.getPatientNumber());
        // 病人账号状态:1:邀请中;2:正常激活状态;0:关闭
        appTenantPatientEntity.setStatus(ClinicPatientStatusEnum.NORMAL.getCode());
        appTenantPatientEntity.setCreator(id);
        appTenantPatientEntity.setModifier(id);
        UpdateEntityTimeUtil.setBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), appTenantPatientEntity);
        appTenantPatientDAO.save(appTenantPatientEntity);
    }

    @Transactional
    @Override
    public void applyPatient(ApplyPatientDTO applyPatientDTO) {
        Long userId = applyPatientDTO.getUserId();
        String tenantCode = applyPatientDTO.getTenantCode();

        // set amplitude property
        AppTenantEntity tenantEntity = appTenantDAO.getByTenantCode(tenantCode);
        Map<String, String> values = new HashMap<>();
        values.put("Clinic", tenantEntity.getName());
        amplitudeProducer.updateAmplitude(userId, values, "$set");

        AppTenantPatientEntity appTenantPatient = appTenantPatientDAO.getByUserIdAndTenantCodeIgnoreStatus(userId, tenantCode);
        if (appTenantPatient != null) {
            if (ClinicPatientStatusEnum.REJECTION.getCode().equals(appTenantPatient.getStatus())) {
                appTenantPatient.setStatus(ClinicPatientStatusEnum.NORMAL.getCode());
                UpdateEntityTimeUtil.updateBaseEntityTime(applyPatientDTO.getTimeZone(), appTenantPatient);
                appTenantPatientDAO.updateById(appTenantPatient);
            }

            // update klaviyo property
            klaviyoProducer.bindClinic(userId, appTenantPatient.getInitEmail(), "yes");
            return;
        }
        // 新增病人
        AppTenantPatientEntity appTenantPatientEntity = new AppTenantPatientEntity();
        appTenantPatientEntity.setTenantCode(tenantCode);
        appTenantPatientEntity.setUserId(userId);
        appTenantPatientEntity.setInitEmail(applyPatientDTO.getEmail());
        appTenantPatientEntity.setFirstName(applyPatientDTO.getFirstName());
        appTenantPatientEntity.setLastName(applyPatientDTO.getLastName());
        appTenantPatientEntity.setNickname(applyPatientDTO.getFirstName() + " " + applyPatientDTO.getLastName());
        Long existCount = appTenantPatientDAO.countTenantPatientByTenantCode(tenantCode);
        appTenantPatientEntity.setPatientNumber(InitPatientNumberUtil.generatePatientNumber(existCount));
        // 病人账号状态:1:邀请中;2:正常激活状态;0:关闭
        appTenantPatientEntity.setStatus(ClinicPatientStatusEnum.NORMAL.getCode());
        appTenantPatientEntity.setCreator(userId);
        appTenantPatientEntity.setModifier(userId);
        UpdateEntityTimeUtil.setBaseEntityTime(applyPatientDTO.getTimeZone(), appTenantPatientEntity);
        appTenantPatientDAO.save(appTenantPatientEntity);

        // 如果关联了医生，创建关联信息
        Long doctorId = applyPatientDTO.getDoctorId();
        if (doctorId != null) {
            AppTenantDoctorEntity doctorEntity = appTenantDoctorDAO.getById(doctorId);
            if (doctorEntity == null) {
                throw new ClinicException("Doctor's information does not exist");
            }
            AppTenantDoctorPatientEntity doctorPatientEntity =
                    buildDoctorPatientRelation(tenantCode, doctorId, appTenantPatientEntity.getId(), userId);
            appTenantDoctorPatientDAO.save(doctorPatientEntity);
        }

        // update klaviyo property
        klaviyoProducer.bindClinic(userId, applyPatientDTO.getEmail(), "yes");
    }
}
