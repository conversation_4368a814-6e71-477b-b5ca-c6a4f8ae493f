package com.mira.clinic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mira.api.clinic.dto.*;
import com.mira.api.clinic.enums.ClinicianStatusEnum;
import com.mira.api.clinic.enums.ClinicPatientStatusEnum;
import com.mira.api.iam.provider.IAuthProvider;
import com.mira.clinic.async.AmplitudeProducer;
import com.mira.clinic.async.EmailProducer;
import com.mira.clinic.async.KlaviyoProducer;
import com.mira.clinic.controller.dto.*;
import com.mira.clinic.controller.vo.TenantDoctorPageVO;
import com.mira.clinic.controller.vo.TenantDoctorVO;
import com.mira.clinic.controller.vo.TenantSettingVO;
import com.mira.clinic.dal.dao.*;
import com.mira.clinic.dal.entity.*;
import com.mira.clinic.dto.AppTenantPatientDTO;
import com.mira.clinic.enums.ClinicDoctorTypeEnum;
import com.mira.clinic.enums.ClinicRoleEnum;
import com.mira.clinic.exception.ClinicException;
import com.mira.clinic.properties.CacheExpireProperties;
import com.mira.clinic.service.IClinicService;
import com.mira.clinic.service.manager.CacheManager;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.RequestSendFlag;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.response.PageResult;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * Clinic服务接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ClinicServiceImpl implements IClinicService {
    @Resource
    private AppTenantDAO appTenantDAO;
    @Resource
    private AppTenantDoctorDAO appTenantDoctorDAO;
    @Resource
    private AppTenantPatientDAO appTenantPatientDAO;
    @Resource
    private AppTenantDoctorPatientDAO appTenantDoctorPatientDAO;

    @Resource
    private AppTenantSettingDAO appTenantSettingDAO;

    @Resource
    private CacheManager cacheManager;

    @Resource
    private IAuthProvider authProvider;
    @Resource
    private AmplitudeProducer amplitudeProducer;
    @Resource
    private KlaviyoProducer klaviyoProducer;

    @Resource
    private EmailProducer emailProducer;

    @Resource
    private CacheExpireProperties cacheExpireProperties;

    @Override
    public UserClinicDTO getClinicInfo(String email) {
        try {
            UserClinicDTO userClinicDTO = new UserClinicDTO();
            AppTenantPatientEntity appTenantPatient = appTenantPatientDAO.getByEmail(email);
            if (ObjectUtils.isEmpty(appTenantPatient)) {
                return null;
            }
            userClinicDTO.setStatus(appTenantPatient.getStatus());
            userClinicDTO.setBindTime(appTenantPatient.getModifyTime());
            AppTenantEntity appTenant = appTenantDAO.getByTenantCode(appTenantPatient.getTenantCode());
            userClinicDTO.setName(appTenant.getName());
            userClinicDTO.setCode(appTenant.getCode());
            userClinicDTO.setIcon(appTenant.getIcon());
            return userClinicDTO;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<UserClinicDTO> listClinicInfos(String email) {
        List<UserClinicDTO> userClinicDTOS = new ArrayList<>();
        try {
            List<AppTenantPatientDTO> appTenantPatientDTOS = appTenantPatientDAO.listByPatientEmail(email);
            if (CollectionUtils.isEmpty(appTenantPatientDTOS)) {
                return userClinicDTOS;
            }
            for (AppTenantPatientDTO appTenantPatientDTO : appTenantPatientDTOS) {
                UserClinicDTO userClinicDTO = new UserClinicDTO();
                Integer status = appTenantPatientDTO.getStatus();
                if (ClinicPatientStatusEnum.REJECTION.getCode().equals(status)) {
                    continue;
                }
                userClinicDTO.setId(appTenantPatientDTO.getTenantId());
                userClinicDTO.setStatus(status);
                userClinicDTO.setBindTime(appTenantPatientDTO.getModifyTime());
                userClinicDTO.setInviteTime(appTenantPatientDTO.getCreateTime());
                AppTenantEntity appTenant = appTenantDAO.getByTenantCode(appTenantPatientDTO.getTenantCode());
                userClinicDTO.setName(appTenant.getName());
                userClinicDTO.setCode(appTenant.getCode());
                userClinicDTO.setIcon(appTenant.getIcon());
                userClinicDTOS.add(userClinicDTO);
            }
        } catch (Exception e) {
            log.error("email:{} 获取用户绑定的clinic信息失败,{}", email, e);
        }
        return userClinicDTOS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @Deprecated(since = "7.6.27")
    public void bind(Long userId, String email) {
        AppTenantPatientEntity patientEntity = appTenantPatientDAO.getByEmail(email);
        if (ObjectUtils.isNotEmpty(patientEntity)) {
            patientEntity.setStatus(ClinicPatientStatusEnum.NORMAL.getCode());
            patientEntity.setUserId(userId);
            UpdateEntityTimeUtil.updateBaseEntityTime(patientEntity.getTimeZone(), patientEntity);
            appTenantPatientDAO.updateById(patientEntity);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @Deprecated(since = "7.6.27")
    public void unbind(Long userId) {
        AppTenantPatientEntity appTenantPatient = appTenantPatientDAO.getByUserId(userId);
        if (ObjectUtils.isNotEmpty(appTenantPatient)) {
            Long patientId = appTenantPatient.getId();
            // 删除病人
            AppTenantPatientEntity appTenantPatientEntity = appTenantPatientDAO.getById(patientId);
            if (appTenantPatientEntity.getUserId() != null) {
                appTenantPatientEntity.setUserId(-appTenantPatientEntity.getUserId());
            }
            appTenantPatientDAO.removePatient(patientId);
            // 删除病人和医生的绑定关系
            appTenantDoctorPatientDAO.removeDoctorPatient(patientId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bindV2(Long userId, String email, String tenantCode) {
        AppTenantPatientEntity patientEntity = appTenantPatientDAO.getByEmailAndTenantCode(email, tenantCode);
        if (ObjectUtils.isNotEmpty(patientEntity)) {
            patientEntity.setStatus(ClinicPatientStatusEnum.NORMAL.getCode());
            patientEntity.setUserId(userId);
            UpdateEntityTimeUtil.updateBaseEntityTime(patientEntity.getTimeZone(), patientEntity);
            appTenantPatientDAO.updateById(patientEntity);
            // set amplitude property
            amplitude(userId, tenantCode, "$set");
            // update klaviyo property
            klaviyo(userId, email, "yes");
        }
    }

    private void amplitude(Long userId, String tenantCode, String operator) {
        AppTenantEntity tenantEntity = appTenantDAO.getByTenantCode(tenantCode);
        Map<String, String> values = new HashMap<>();
        values.put("Clinic", tenantEntity.getName());
        amplitudeProducer.updateAmplitude(userId, values, operator);
    }

    private void klaviyo(Long userId, String email, String value) {
        klaviyoProducer.bindClinic(userId, email, value);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void unbindV2(Long userId, String tenantCode) {
        AppTenantPatientEntity appTenantPatient = appTenantPatientDAO.getByUserIdAndTenantCode(userId, tenantCode);
        if (ObjectUtils.isNotEmpty(appTenantPatient)) {
            Long patientId = appTenantPatient.getId();
            // 删除病人
            AppTenantPatientEntity appTenantPatientEntity = appTenantPatientDAO.getById(patientId);
            if (appTenantPatientEntity.getUserId() != null) {
                appTenantPatientEntity.setUserId(-appTenantPatientEntity.getUserId());
            }
            appTenantPatientDAO.removePatient(patientId);
            // 删除病人和医生的绑定关系
            appTenantDoctorPatientDAO.removeDoctorPatient(patientId);

            // unset amplitude property
            amplitude(userId, tenantCode, "$unset");
            // update klaviyo property
            klaviyo(userId, appTenantPatient.getInitEmail(), "no");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectInvite(Long userId, String tenantCode) {
        AppTenantPatientEntity patientEntity = appTenantPatientDAO.getByUserIdAndTenantCodeIgnoreStatus(userId, tenantCode);
        if (ObjectUtils.isEmpty(patientEntity)) {
            return;
        }
        patientEntity.setStatus(ClinicPatientStatusEnum.REJECTION.getCode());
        patientEntity.setUserId(userId);
        UpdateEntityTimeUtil.updateBaseEntityTime(patientEntity.getTimeZone(), patientEntity);
        appTenantPatientDAO.updateById(patientEntity);

    }

    @Override
    public ClinicDTO getClinicByCode(String tenantCode) {
        AppTenantEntity tenant = appTenantDAO.getByTenantCode(tenantCode);
        if (ObjectUtils.isEmpty(tenant)) {
            return null;
        }
        ClinicDTO clinicDTO = new ClinicDTO();
        clinicDTO.setName(tenant.getName());
        clinicDTO.setCode(tenant.getCode());
        clinicDTO.setIcon(tenant.getIcon());
        clinicDTO.setDescription(tenant.getDescription());
        return clinicDTO;
    }

    @Override
    public PageResult<TenantDoctorPageVO> doctorPage(TenantDoctorPageDTO pageDTO) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(appTenantDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        String tenantCode = appTenantDoctor.getTenantCode();

        Integer role = ClinicRoleEnum.DOCTOR.getCode();

        Page<AppTenantDoctorEntity> page = appTenantDoctorDAO.pageByTenantCodeAndRole(pageDTO, tenantCode, role, pageDTO.getKeyword());
        if (ObjectUtils.isEmpty(page)) {
            return new PageResult<>();
        }
        List<AppTenantDoctorEntity> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageResult<>();
        }



        List<TenantDoctorPageVO> tenantDoctorPageVOS = new ArrayList<>();

        for (AppTenantDoctorEntity appTenantDoctorEntity : records) {
            TenantDoctorPageVO tenantDoctorPageVO = new TenantDoctorPageVO();
            BeanUtil.copyProperties(appTenantDoctorEntity, tenantDoctorPageVO);

            tenantDoctorPageVO.setRelatedDoctors(new ArrayList<>());
            tenantDoctorPageVOS.add(tenantDoctorPageVO);

            //将处在邀请中的账号，校验是否邀请过期
            Integer status = appTenantDoctorEntity.getStatus();
            if(ClinicianStatusEnum.INVITING.getCode().equals(status)) {
                Long inviteTime = appTenantDoctorEntity.getInviteTime();
                if(inviteTime == null) {
                    continue;
                }
                if (inviteTime + cacheExpireProperties.getInvite() * 60 * 1000 < System.currentTimeMillis()) {
                    appTenantDoctorEntity.setStatus(ClinicianStatusEnum.INVITATION_EXPIRED.getCode());
                    appTenantDoctorDAO.updateById(appTenantDoctorEntity);
                }
            }
        }

        return new PageResult<>(tenantDoctorPageVOS, page.getTotal(), page.getSize(), page.getCurrent());
    }

    @Override
    public List<TenantDoctorVO> doctorList(TenantDoctorListDTO pageDTO) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(appTenantDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        String tenantCode = appTenantDoctor.getTenantCode();

        Integer role = ClinicRoleEnum.DOCTOR.getCode();


        List<AppTenantDoctorEntity> appTenantDoctorEntities = appTenantDoctorDAO.listByTenantCodeAndRole(tenantCode, role, pageDTO.getKeyword());
        List<TenantDoctorVO> tenantDoctorVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(appTenantDoctorEntities)) {
            appTenantDoctorEntities.forEach(appTenantDoctorEntity -> {
                TenantDoctorVO tenantDoctorVO = new TenantDoctorVO();
                BeanUtil.copyProperties(appTenantDoctorEntity, tenantDoctorVO);
                tenantDoctorVOS.add(tenantDoctorVO);
            });
        }
        return tenantDoctorVOS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void doctorRemove(Long tenantId) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity loginDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(loginDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        // 删除医生/护士
        AppTenantDoctorEntity tenantDoctor = appTenantDoctorDAO.getById(tenantId);
        Integer role = tenantDoctor.getRole();
        tenantDoctor.setEmail(tenantDoctor.getEmail()
                + ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN)
                + new Random().nextInt(1000));
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, tenantDoctor);
        appTenantDoctorDAO.updateById(tenantDoctor);
        appTenantDoctorDAO.removeById(tenantId);

        // 删除医生/护士令牌
        String clinicToken = cacheManager.getClinicMarkToken(tenantId);
        if (StringUtils.isNotBlank(clinicToken)) {
            authProvider.deleteToken(clinicToken, UserTypeEnum.CLINIC_USER.getType());
        }

        // 如果是医生，删除医生病人绑定关系
        if (ClinicRoleEnum.DOCTOR.getCode().equals(role)) {
            appTenantDoctorPatientDAO.removeByDoctorId(tenantId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void patientRemove(Long patientId) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(appTenantDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }

        // unset amplitude property
        AppTenantPatientEntity patientEntity = appTenantPatientDAO.getById(patientId);
        if (Objects.nonNull(patientEntity)) {
            Long userId = patientEntity.getUserId();
            amplitude(userId, patientEntity.getTenantCode(), "$unset");
            // update klaviyo property
            klaviyo(userId, patientEntity.getInitEmail(), "no");
        }

        // 这里都使用物理删除
        appTenantPatientDAO.removePatient(patientId);
        appTenantDoctorPatientDAO.removeDoctorPatient(patientId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editSetting(EditSettingDTO editSettingDTO) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(appTenantDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String tenantCode = appTenantDoctor.getTenantCode();

        AppTenantSettingEntity tenantSettingEntity = appTenantSettingDAO.getByTenantCode(tenantCode);
        if (tenantSettingEntity == null) {
            // save
            tenantSettingEntity = new AppTenantSettingEntity();
            tenantSettingEntity.setTenantCode(tenantCode);
            tenantSettingEntity.setNotificationFirstTest(1);
            tenantSettingEntity.setNotificationLhSurge(1);
            tenantSettingEntity.setNotificationPeriodEdited(1);
            tenantSettingEntity.setNotificationPeriodStarted(1);
            tenantSettingEntity.setNotificationPositiveHCG(1);
            tenantSettingEntity.setNotificationPregnant(1);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, tenantSettingEntity);
            appTenantSettingDAO.save(tenantSettingEntity);
        } else {
            // update
            BeanUtil.copyProperties(editSettingDTO, tenantSettingEntity);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, tenantSettingEntity);
            appTenantSettingDAO.updateById(tenantSettingEntity);
        }
    }

    @Override
    public TenantSettingVO setting() {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(appTenantDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String tenantCode = appTenantDoctor.getTenantCode();

        TenantSettingVO tenantSettingVO = new TenantSettingVO();
        tenantSettingVO.setTenantCode(tenantCode);
        tenantSettingVO.setNotificationFirstTest(1);
        tenantSettingVO.setNotificationLhSurge(1);
        tenantSettingVO.setNotificationPeriodEdited(1);
        tenantSettingVO.setNotificationPeriodStarted(1);
        tenantSettingVO.setNotificationPositiveHCG(1);
        tenantSettingVO.setNotificationPregnant(1);
        AppTenantSettingEntity tenantSettingEntity = appTenantSettingDAO.getByTenantCode(tenantCode);
        if (tenantSettingEntity != null) {
            BeanUtil.copyProperties(tenantSettingEntity, tenantSettingVO);
        }
        return tenantSettingVO;
    }

    @Override
    @Deprecated(since = "7.6.27")
    public UserPatientDTO getUserPatientDTO(Long userId) {
        UserPatientDTO userPatientDTO = null;
        AppTenantPatientEntity tenantPatientEntity = appTenantPatientDAO.getByUserId(userId);
        if (tenantPatientEntity != null) {
            userPatientDTO = new UserPatientDTO();
            userPatientDTO.setStatus(tenantPatientEntity.getStatus());
            userPatientDTO.setTenantCode(tenantPatientEntity.getTenantCode());
            return userPatientDTO;
        }
        return userPatientDTO;
    }

    @Override
    public List<UserPatientDTO> listUserPatientDTOs(Long userId) {
        List<UserPatientDTO> userPatientDTOs = new ArrayList<>();
        List<AppTenantPatientEntity> appTenantPatientEntities = appTenantPatientDAO.listByUserId(userId);
        if (CollectionUtils.isEmpty(appTenantPatientEntities)) {
            return userPatientDTOs;
        }
        appTenantPatientEntities
                .stream()
                .forEach(tenantPatientEntity -> {
                    UserPatientDTO userPatientDTO = new UserPatientDTO();
                    userPatientDTO.setStatus(tenantPatientEntity.getStatus());
                    userPatientDTO.setTenantCode(tenantPatientEntity.getTenantCode());
                    userPatientDTOs.add(userPatientDTO);
                });
        return userPatientDTOs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initCreateClinic(InitCreateClinicDTO initCreateClinicDTO) {
        String timeZone = "Asia/Shanghai";
        String code = initCreateClinicDTO.getClinicCode();
        String password = initCreateClinicDTO.getPassword();
        AppTenantEntity appTenantEntity = appTenantDAO.getOne(
                Wrappers.<AppTenantEntity>lambdaQuery().eq(AppTenantEntity::getCode, code)
        );
        if (appTenantEntity != null) {
            throw new ClinicException("clinic: " + code + " has existed");
        }
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getByEmail(initCreateClinicDTO.getEmail());
        if (appTenantDoctor != null) {
            throw new ClinicException("email: " + initCreateClinicDTO.getEmail() + "has existed in clinic system, " +
                    "with tenant code " + appTenantDoctor.getTenantCode());
        }

        appTenantEntity = new AppTenantEntity();
        appTenantEntity.setCode(code);
        appTenantEntity.setName(initCreateClinicDTO.getClinicName());
        appTenantEntity.setIcon(initCreateClinicDTO.getIcon());
        appTenantEntity.setType(initCreateClinicDTO.getClinicType());
        appTenantEntity.setNotificationIcon(initCreateClinicDTO.getNotificationIcon());
        appTenantEntity.setDescription(initCreateClinicDTO.getWebsiteUrl());
        appTenantEntity.setInitPassword(password);
        appTenantEntity.setTimeZone(timeZone);
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appTenantEntity);
        appTenantDAO.save(appTenantEntity);

        appTenantDoctor = new AppTenantDoctorEntity();
        appTenantDoctor.setTenantCode(code);
        appTenantDoctor.setEmail(initCreateClinicDTO.getEmail());
        appTenantDoctor.setName(initCreateClinicDTO.getEmail());
        appTenantDoctor.setStatus(ClinicianStatusEnum.CONFIRMED.getCode());
        appTenantDoctor.setDoctorType(ClinicDoctorTypeEnum.ADMIN.getCode());
        if (initCreateClinicDTO.getClinicType() == ClinicDoctorTypeEnum.ADMIN.getCode()) {
            appTenantDoctor.setRole(ClinicRoleEnum.CLINIC_ADMIN.getCode());
        } else if (initCreateClinicDTO.getClinicType() == ClinicDoctorTypeEnum.CLINICIAN.getCode()) {
            appTenantDoctor.setRole(ClinicRoleEnum.DOCTOR.getCode());
        }

        String salt = RandomStringUtils.randomAlphanumeric(20);
        appTenantDoctor.setPassword(new Sha256Hash(password, salt).toHex());
        appTenantDoctor.setSalt(salt);
        appTenantEntity.setTimeZone(timeZone);
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appTenantDoctor);
        appTenantDoctorDAO.save(appTenantDoctor);
    }

    @Override
    public void inviteClinic(InviteClinicDTO inviteClinicDTO, int sendFlag) {
        String timeZone = "Asia/Shanghai";
        String code = inviteClinicDTO.getClinicCode();
        AppTenantEntity appTenant = appTenantDAO.getOne(
                Wrappers.<AppTenantEntity>lambdaQuery().eq(AppTenantEntity::getCode, code)
        );
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getByEmail(inviteClinicDTO.getEmail());

        if (RequestSendFlag.FIRST_SEND == sendFlag) {
            if (appTenant != null) {
                throw new ClinicException("clinic: " + code + " has existed");
            }
            if (appTenantDoctor != null) {
                throw new ClinicException("email: " + inviteClinicDTO.getEmail() + "has existed in clinic system, " +
                        "with tenant code " + appTenantDoctor.getTenantCode());
            }
            //创建用户，状态为邀请中
            appTenant = new AppTenantEntity();
            appTenant.setCode(code);
            appTenant.setName(inviteClinicDTO.getClinicName());
            appTenant.setIcon(inviteClinicDTO.getIcon());
            appTenant.setType(inviteClinicDTO.getClinicType());
            appTenant.setNotificationIcon(inviteClinicDTO.getNotificationIcon());
            appTenant.setDescription(inviteClinicDTO.getWebsiteUrl());
            appTenant.setInitPassword("init");
            appTenant.setTimeZone(timeZone);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appTenant);
            appTenantDAO.save(appTenant);

        appTenantDoctor = new AppTenantDoctorEntity();
        appTenantDoctor.setTenantCode(code);
        appTenantDoctor.setEmail(inviteClinicDTO.getEmail());
        appTenantDoctor.setName(inviteClinicDTO.getEmail());
        appTenantDoctor.setStatus(ClinicianStatusEnum.INVITING.getCode());
        appTenantDoctor.setDoctorType(ClinicDoctorTypeEnum.ADMIN.getCode());
        appTenantDoctor.setInviteTime(System.currentTimeMillis());
        if (inviteClinicDTO.getClinicType() == ClinicDoctorTypeEnum.ADMIN.getCode()) {
            appTenantDoctor.setRole(ClinicRoleEnum.CLINIC_ADMIN.getCode());
        } else if (inviteClinicDTO.getClinicType() == ClinicDoctorTypeEnum.CLINICIAN.getCode()) {
            appTenantDoctor.setRole(ClinicRoleEnum.DOCTOR.getCode());
        }

            String salt = RandomStringUtils.randomAlphanumeric(20);
            appTenantDoctor.setPassword("init123456");
            appTenantDoctor.setSalt(salt);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appTenantDoctor);
            appTenantDoctorDAO.save(appTenantDoctor);

            //发送email
            Map<String, String> emailVariable = emailProducer.inviteClinic(inviteClinicDTO, appTenantDoctor.getId());
            log.info("invite Clinic account:{} ", inviteClinicDTO);
            // save cache
            String hash = emailVariable.get("userHash");
            cacheManager.cacheRegister(appTenantDoctor, hash);

        } else if (RequestSendFlag.RESEND == sendFlag) {
            if (appTenantDoctor == null) {
                throw new ClinicException(BizCodeEnum.INTERNAL_SERVER_ERROR);
            }
            if (appTenantDoctor.getStatus() == ClinicianStatusEnum.INVITATION_EXPIRED.getCode()) {
                appTenantDoctor.setStatus(ClinicianStatusEnum.INVITING.getCode());
                appTenantDoctor.setInviteTime(System.currentTimeMillis());
                UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, appTenantDoctor);
                appTenantDoctorDAO.updateById(appTenantDoctor);
                // 发送邮件
                Map<String, String> emailVariable = emailProducer.inviteClinic(inviteClinicDTO, appTenantDoctor.getId());
                log.info("invite Clinic account resend:{} ", inviteClinicDTO);
                // save cache
                String hash = emailVariable.get("userHash");
                cacheManager.cacheRegister(appTenantDoctor, hash);
            }
        }
    }

    @Override
    public ClinicPageResponseDTO clinicPage(ClinicPageRequestDTO clinicPageRequestDTO) {
        int currIndex = (clinicPageRequestDTO.getCurrent() - 1) * clinicPageRequestDTO.getSize();
        List<ClinicListDTO> clinicListDTOS = appTenantDAO.listByPage(currIndex, clinicPageRequestDTO.getSize(), clinicPageRequestDTO.getKeyword());
        Long count = appTenantDAO.countByKeyword(clinicPageRequestDTO.getKeyword());
        ClinicPageResponseDTO clinicPageResponseDTO = new ClinicPageResponseDTO();
        clinicPageResponseDTO.setClinicListDTOS(clinicListDTOS);
        clinicPageResponseDTO.setTotal(count);

        for (ClinicListDTO clinicListDTO : clinicListDTOS) {
            //将处在邀请中的账号，校验是否邀请过期
            Integer status = clinicListDTO.getStatus();
            if(ClinicianStatusEnum.INVITING.getCode().equals(status)) {
                Long inviteTime = clinicListDTO.getInviteTime();
                if(inviteTime == null) {
                    continue;
                }
                if (inviteTime + cacheExpireProperties.getInvite() * 60 * 1000 < System.currentTimeMillis()) {
                    AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(clinicListDTO.getDoctorId());
                    appTenantDoctor.setStatus(ClinicianStatusEnum.INVITATION_EXPIRED.getCode());
                    appTenantDoctorDAO.updateById(appTenantDoctor);
                }
            }
        }


        return clinicPageResponseDTO;
    }

    @Override
    public List<ClinicListDTO> allClinic() {
        return appTenantDAO.all();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editClinicInfo(EditClinicDTO editClinicDTO) {
        AppTenantEntity appTenantEntity = appTenantDAO.getById(editClinicDTO.getId());
        String existTenantCode = appTenantEntity.getCode();
        String newClinicCode = editClinicDTO.getClinicCode();
        Integer oldType = appTenantEntity.getType();
        Integer newType = editClinicDTO.getClinicType();
        //医生类型诊所修改诊所类型，诊所邮箱要改成管理员 type 1-->0的时候

        //不允许修改tenantCode
        if (!existTenantCode.equals(newClinicCode)) {
            throw new ClinicException("clinic code:【" + existTenantCode + "】can not be changed.");
        }

        String email = editClinicDTO.getEmail();
        List<AppTenantDoctorEntity> appTenantDoctorEntities = appTenantDoctorDAO.listByTenantCode(existTenantCode);
        appTenantEntity.setCode(existTenantCode);
        appTenantEntity.setName(editClinicDTO.getClinicName());
        appTenantEntity.setIcon(editClinicDTO.getIcon());
        appTenantEntity.setNotificationIcon(editClinicDTO.getNotificationIcon());
        appTenantEntity.setDescription(editClinicDTO.getWebsiteUrl());
        appTenantEntity.setType(editClinicDTO.getClinicType());
        UpdateEntityTimeUtil.setBaseEntityTime(appTenantEntity.getTimeZone(), appTenantEntity);
        appTenantDAO.updateById(appTenantEntity);

        AppTenantDoctorEntity appTenantDoctorEntity =
                appTenantDoctorEntities.stream()
                                       .filter(tenantDoctor -> ClinicDoctorTypeEnum.ADMIN.getCode().equals(tenantDoctor.getDoctorType()))
                                       .findFirst()
                                        .orElse(null);
        if (appTenantDoctorEntity != null) {
            if (!appTenantDoctorEntity.getEmail().equals(email)) {
                AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getByEmail(email);
                if (appTenantDoctor != null) {
                    throw new ClinicException("email: " + email + "has existed in clinic system, " +
                            "with tenant code " + appTenantDoctor.getTenantCode() + "role:" + appTenantDoctor.getRole());
                }
            }
            appTenantDoctorEntity.setEmail(email);
            appTenantDoctorEntity.setTenantCode(newClinicCode);
            appTenantDoctorEntity.setName(email);
            //doctor_type不会改变
            appTenantDoctorEntity.setDoctorType(ClinicDoctorTypeEnum.ADMIN.getCode());
            if (ClinicDoctorTypeEnum.ADMIN.getCode().equals(newType) && ClinicDoctorTypeEnum.CLINICIAN.getCode().equals(oldType)) {
                appTenantDoctorEntity.setRole(ClinicRoleEnum.CLINIC_ADMIN.getCode());
            }
            UpdateEntityTimeUtil.updateBaseEntityTime(appTenantDoctorEntity.getTimeZone(), appTenantDoctorEntity);
            appTenantDoctorDAO.updateById(appTenantDoctorEntity);
        }
    }

    @Override
    public void deleteClinicInfo(Long id) {
        AppTenantEntity appTenantEntity = appTenantDAO.getById(id);
        String existTenantCode = appTenantEntity.getCode();
        List<AppTenantDoctorEntity> appTenantDoctorEntities = appTenantDoctorDAO.listByTenantCode(existTenantCode);
        //如果clinic已经有医生，病人了，不允许删除
        if (appTenantDoctorEntities.size() >= 2) {
            throw new ClinicException("tenant code:【" + existTenantCode + "】can not be changed " +
                    "because of having existed "+appTenantDoctorEntities.size()+" doctors.");
        }
        AppTenantDoctorEntity appTenantDoctorEntity =
                appTenantDoctorEntities.stream()
                                       .filter(appTenantDoctorEntity1 -> ClinicDoctorTypeEnum.ADMIN.getCode().equals(appTenantDoctorEntity1.getDoctorType()))
                                       .findFirst()
                                       .orElse(null);
        if (appTenantDoctorEntity != null) {
            appTenantDAO.getBaseMapper().deleteClinicInfo(id);
            appTenantDoctorDAO.getBaseMapper().deleteClinicManager(appTenantDoctorEntity.getId());
        }
    }

    @Override
    public void editName(String name) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicDoctorTypeEnum.ADMIN.getCode().equals(appTenantDoctor.getDoctorType())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        if (StringUtils.isBlank(name)) {
            return;
        }
        AppTenantEntity appTenantEntity = appTenantDAO.getByTenantCode(appTenantDoctor.getTenantCode());
        appTenantEntity.setName(name);
        UpdateEntityTimeUtil.updateBaseEntityTime(appTenantEntity.getTimeZone(), appTenantEntity);
        appTenantDAO.updateById(appTenantEntity);
        //清理缓存 暂无
        //todo 给mira desk管理员发送email通知，或者站内信


    }
}
