package com.mira.clinic.controller;

import com.mira.clinic.controller.dto.InitDoctorDTO;
import com.mira.clinic.controller.dto.InviteDoctorDTO;
import com.mira.clinic.controller.dto.InviteNurseDTO;
import com.mira.clinic.controller.dto.InvitePatientDTO;
import com.mira.clinic.service.IInviteService;
import com.mira.core.annotation.Anonymous;
import com.mira.core.consts.RequestSendFlag;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 邀请Clinician/病人
 *
 * <AUTHOR>
 */
@Api(tags = "邀请Clinician or 病人")
@RestController
@RequestMapping("/tenant/invite")
public class InviteController {
    @Resource
    private IInviteService iInviteService;

    @ApiOperation("邀请Doctor")
    @PostMapping("/doctor")
    public void inviteDoctor(@RequestBody InviteDoctorDTO inviteDoctorDTO) {
        iInviteService.inviteDoctor(inviteDoctorDTO, RequestSendFlag.FIRST_SEND);
    }

    @ApiOperation("邀请Doctor,重新发送")
    @PostMapping("/doctor/resend")
    public void inviteDoctorResend(@RequestBody InviteDoctorDTO inviteDoctorDTO) {
        iInviteService.inviteDoctor(inviteDoctorDTO, RequestSendFlag.RESEND);
    }


    @Anonymous
    @ApiOperation("初始化Doctor")
    @PostMapping("/init-doctor")
    public void initDoctor(@RequestBody InitDoctorDTO initDoctorDTO) {
        iInviteService.initDoctor(initDoctorDTO);
    }

    @ApiOperation("邀请Patient")
    @PostMapping("/patient")
    public void invitePatient(@RequestBody InvitePatientDTO invitePatientDTO) {
        iInviteService.invitePatient(invitePatientDTO, RequestSendFlag.FIRST_SEND);
    }

    @ApiOperation("邀请Patient,重新发送")
    @PostMapping("/patient/resend")
    public void invitePatientResend(@RequestBody InvitePatientDTO invitePatientDTO) {
        iInviteService.invitePatient(invitePatientDTO, RequestSendFlag.RESEND);
    }

    @Anonymous
    @ApiOperation("用户授权同意成为病人")
    @PostMapping("/grant-patient")
    public void grantPatient(@RequestParam String hash) {
        iInviteService.grantPatient(hash);
    }

    @ApiOperation("批量添加病人（仅开发人员使用）")
    @PostMapping("/batch-add-patient")
    public void batchAddPatient(@RequestParam String key) {
        if (!"2jhsdseuo8dbh@kkiuio2139nb78u872nd".equals(key)) {
            return;
        }
        iInviteService.batchAddPatient();
    }
}
