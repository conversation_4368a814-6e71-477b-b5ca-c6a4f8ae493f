package com.mira.job.service.manager;

import com.mira.core.util.ThreadPoolUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.dal.dao.master.AppUserInfoDAO;
import com.mira.job.properties.JobProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.ehcache.Cache;
import org.ehcache.CacheManager;
import org.ehcache.config.CacheConfiguration;
import org.ehcache.config.builders.CacheConfigurationBuilder;
import org.ehcache.config.builders.CacheManagerBuilder;
import org.ehcache.config.builders.ExpiryPolicyBuilder;
import org.ehcache.config.builders.PooledExecutionServiceConfigurationBuilder;
import org.ehcache.config.builders.ResourcePoolsBuilder;
import org.ehcache.config.units.EntryUnit;
import org.ehcache.config.units.MemoryUnit;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;

/**
 * ehcache manager
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PushUserInfoCacheManager {
    @Resource
    private AppUserInfoDAO appUserInfoDAO;
    @Resource
    private JobProperties jobProperties;

    private final CacheManager cacheManager;
    private final String cacheFileName = "pushuserinfo-cache";
    private final String cacheIndexKey = "pushuserinfo-cache-index";

    public PushUserInfoCacheManager() {
        // 配置缓存，优先使用磁盘存储，只保留少量热点数据在内存中
        CacheConfiguration<String, ArrayList> configuration = CacheConfigurationBuilder
                .newCacheConfigurationBuilder(String.class, ArrayList.class, ResourcePoolsBuilder.newResourcePoolsBuilder()
                        // 只在堆内存中保留极少量条目（热点数据）
                        .heap(2, EntryUnit.ENTRIES)
                        // 使用堆外内存作为二级缓存，减轻堆内存压力
                        .offheap(1, MemoryUnit.GB)
                        // 主要存储在磁盘上，持久化为true
                        .disk(100, MemoryUnit.GB, true))
                // 设置过期时间
                .withExpiry(ExpiryPolicyBuilder.timeToLiveExpiration(Duration.ofHours(2)))
                // 启用磁盘优先策略
                .withDiskStoreThreadPool("disk-operations", 2)
                .build();

        // 配置缓存管理器
        this.cacheManager = CacheManagerBuilder.newCacheManagerBuilder()
                // 指定持久化目录
                .with(CacheManagerBuilder.persistence("./cache"))
                // 启用更新写入模式，减少内存占用
                .using(PooledExecutionServiceConfigurationBuilder
                        .newPooledExecutionServiceConfigurationBuilder()
                        .defaultPool("default-disk-pool", 1, 5)
                        .pool("disk-operations", 1, 3)
                        .build())
                .withCache(cacheFileName, configuration)
                .build(true);
    }

    private void put(String key, ArrayList value) {
        Cache<String, ArrayList> cache = cacheManager.getCache(cacheFileName, String.class, ArrayList.class);
        cache.put(key, value);
    }

    private ArrayList get(String key) {
        Cache<String, ArrayList> cache = cacheManager.getCache(cacheFileName, String.class, ArrayList.class);
        return cache.get(key);
    }

    /**
     * 从缓存中移除指定键的数据
     */
    private void remove(String key) {
        Cache<String, ArrayList> cache = cacheManager.getCache(cacheFileName, String.class, ArrayList.class);
        cache.remove(key);
        log.debug("已从缓存中移除数据: {}", key);
    }

    /**
     * 清空所有缓存
     * 在内存压力大时可以调用此方法释放内存
     */
    public void clearAllCache() {
        try {
            ArrayList<String> cacheIndexList = get(cacheIndexKey);
            if (CollectionUtils.isNotEmpty(cacheIndexList)) {
                for (String index : cacheIndexList) {
                    remove(index);
                }
                log.info("已清空所有缓存数据，共 {} 个分批", cacheIndexList.size());
            }
            remove(cacheIndexKey);
        } catch (Exception e) {
            log.error("清空缓存异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取缓存状态信息
     * 返回缓存中的分批数量和估计的数据量
     */
    public Map<String, Object> getCacheStatus() {
        Map<String, Object> status = new HashMap<>();
        try {
            ArrayList<String> cacheIndexList = get(cacheIndexKey);
            int batchCount = CollectionUtils.isEmpty(cacheIndexList) ? 0 : cacheIndexList.size();
            status.put("缓存分批数", batchCount);

            // 估算总数据量
            long totalRecords = 0;
            if (CollectionUtils.isNotEmpty(cacheIndexList)) {
                for (String index : cacheIndexList) {
                    ArrayList records = get(index);
                    if (records != null) {
                        totalRecords += records.size();
                    }
                }
            }
            status.put("缓存总条目", totalRecords);
            status.put("缓存状态", batchCount > 0 ? "正常" : "未初始化");
        } catch (Exception e) {
            log.error("获取缓存状态异常: {}", e.getMessage(), e);
            status.put("缓存状态", "异常: " + e.getMessage());
        }
        return status;
    }

    public List<String> getCacheIndex() {
        ArrayList cacheIndexList = get(cacheIndexKey);
        if (0 == jobProperties.getCacheSwitch()) {
            remove(cacheIndexKey);
            if (CollectionUtils.isNotEmpty(cacheIndexList)) {
                cacheIndexList = null;
            }
        }
        if (CollectionUtils.isEmpty(cacheIndexList)) {
            synchronized (PushUserInfoCacheManager.class) {
                // double check
                cacheIndexList = get(cacheIndexKey);
                if (CollectionUtils.isNotEmpty(cacheIndexList)) {
                    return cacheIndexList;
                }
                getPushUserInfoCache("-1");
                return get(cacheIndexKey);
            }
        }
        return cacheIndexList;
    }

    /**
     * 获取用户信息缓存
     * 分批加载，避免一次性加载全部数据到内存
     */
    public ArrayList<PushUserInfoDTO> getPushUserInfoCache(String cacheIndex) {
        // 先从缓存中查询
        ArrayList pushUserInfoList = get(cacheIndex);
        if (CollectionUtils.isNotEmpty(pushUserInfoList)) {
            return pushUserInfoList;
        }

        // 缓存不存在，从数据库查询
        synchronized (PushUserInfoCacheManager.class) {
            // 双重检查
            pushUserInfoList = get(cacheIndex);
            if (CollectionUtils.isNotEmpty(pushUserInfoList)) {
                return pushUserInfoList;
            }

            // 分批查询参数设置
            int queryBatch = 5000;
            long pageSize = 0;
            long recordCount = appUserInfoDAO.getCount();
            log.info("用户记录总数: {}", recordCount);

            // 计算分批数量
            long cdCount = recordCount % queryBatch == 0 ? recordCount / queryBatch : recordCount / queryBatch + 1;
            CountDownLatch cd = new CountDownLatch((int) cdCount);
            long queryIndex = cdCount;
            List<String> queryIndexList = Collections.synchronizedList(new ArrayList<>());

            // 分批处理
            while (recordCount > 0) {
                long finalPageSize = pageSize;
                long finalQueryIndex = queryIndex;
                CompletableFuture.runAsync(() -> {
                    try {
                        // 查询数据库
                        ArrayList<PushUserInfoDTO> recordList = appUserInfoDAO.getBaseMapper().queryUserInfo(finalPageSize, queryBatch);
                        if (CollectionUtils.isNotEmpty(recordList)) {
                            String key = String.valueOf(finalQueryIndex);
                            queryIndexList.add(key);
                            // 将数据存入缓存
                            put(key, recordList);
                            log.debug("成功缓存用户数据分批: {}, 数量: {}", key, recordList.size());
                        }
                    } catch (Exception e) {
                        log.error("查询用户信息异常: {}", e.getMessage(), e);
                    } finally {
                        cd.countDown();
                    }
                }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
                    cd.countDown();
                    log.error("获取用户信息异常", ex);
                    return null;
                });

                pageSize += queryBatch;
                recordCount -= queryBatch;
                queryIndex--;
            }

            // 等待所有异步任务完成
            try {
                cd.await();
                log.info("所有用户数据缓存完成，共 {} 批", cdCount);
            } catch (InterruptedException ex) {
                log.error("等待缓存加载完成时被中断: {}", ex.getMessage(), ex);
            }

            // 缓存索引
            if (CollectionUtils.isNotEmpty(queryIndexList)) {
                put(cacheIndexKey, new ArrayList<>(queryIndexList));
                log.info("缓存索引已更新，共 {} 个分批", queryIndexList.size());
            }
        }

        return get(cacheIndex);
    }
}
