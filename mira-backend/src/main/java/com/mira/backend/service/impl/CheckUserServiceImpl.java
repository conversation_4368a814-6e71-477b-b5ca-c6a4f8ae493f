package com.mira.backend.service.impl;

import com.mira.api.clinic.dto.UserPatientDTO;
import com.mira.api.clinic.provider.IClinicProvider;
import com.mira.api.iam.dto.AuthTokenDTO;
import com.mira.api.iam.provider.IAuthProvider;
import com.mira.api.mongo.provider.IAccessLogProvider;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.dto.user.*;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.api.user.provider.IUserProvider;
import com.mira.backend.consts.DemoUserConsts;
import com.mira.backend.dal.dao.user.SnToRecordDAO;
import com.mira.backend.dal.dao.user.UserReminderComplaintDAO;
import com.mira.backend.dal.entity.user.AppUserEntity;
import com.mira.backend.dal.entity.user.SnToRecordEntity;
import com.mira.backend.dal.mapper.user.AppUserMapper;
import com.mira.backend.dto.LoginAdminUserDTO;
import com.mira.backend.exception.BackendException;
import com.mira.backend.manager.CacheManager;
import com.mira.backend.pojo.param.user.ModifyEmailParam;
import com.mira.backend.pojo.vo.user.UserInfoVO;
import com.mira.backend.pojo.vo.user.UserSearchVO;
import com.mira.backend.service.IAdminLoginService;
import com.mira.backend.service.ICheckUserService;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.response.CommonResult;
import com.mira.core.util.AgeUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-07-10
 **/
@Slf4j
@Service
public class CheckUserServiceImpl implements ICheckUserService {
    @Resource
    private IAdminLoginService adminLoginService;
    @Resource
    private IAuthProvider authProvider;
    @Resource
    private IUserProvider userProvider;
    @Resource
    private ISsoProvider ssoProvider;
    @Resource
    private IClinicProvider clinicProvider;
    @Resource
    private IAccessLogProvider accessLogProvider;

    @Resource
    private SnToRecordDAO snToRecordDAO;

    @Resource
    private CacheManager cacheManager;

    @Resource
    private UserReminderComplaintDAO userReminderComplaintDAO;


    @Resource
    private AppUserMapper appUserMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createCheckToken(Long userId) throws Exception {
        Long adminId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        adminLoginService.checkUserInfoPermission(userId);

        AppUserDTO appUserDTO = userProvider.getUserById(userId).getData();
        // 设置用户类别
        ContextHolder.put(HeaderConst.USER_TYPE, UserTypeEnum.APP_USER.getType());
        // 发放令牌
        CommonResult<AuthTokenDTO> tokenResult = authProvider.generalAccessToken(appUserDTO.getEmail(), appUserDTO.getPassword());
        String checkToken = "check:".concat(adminId.toString()).concat(":").concat(userId.toString()).concat(":").concat(tokenResult.getData().getAccess_token());
        return checkToken;
    }

    @Override
    public String createCheckTokenV2(String email) throws Exception {
        Long adminId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        if (StringUtils.isBlank(email)) {
            return null;
        }
        AppUserDTO appUserDTO = userProvider.getUserByEmail(email).getData();
        Long userId = appUserDTO.getId();
        adminLoginService.checkUserInfoPermission(userId);
        // 设置用户类别
        ContextHolder.put(HeaderConst.USER_TYPE, UserTypeEnum.APP_USER.getType());
        // 发放令牌
        CommonResult<AuthTokenDTO> tokenResult = authProvider.generalAccessToken(appUserDTO.getEmail(), appUserDTO.getPassword());
        String checkToken = "check:".concat(adminId.toString()).concat(":").concat(userId.toString()).concat(":").concat(tokenResult.getData().getAccess_token());
        return checkToken;
    }

    @Override
    public List<UserSearchVO> searchDeskUser(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return null;
        }

        LoginAdminUserDTO adminUserDTO = adminLoginService.adminInfo();
        Set<String> permissionCodes = adminUserDTO.getPermissionCodes();
        Set<String> checkTokenRangeSet = permissionCodes
                .stream()
                .filter(permissionCode -> permissionCode.startsWith("data:checkToken:"))
                .map(permissionCode -> permissionCode.substring("data:checkToken:".length()))
                .collect(Collectors.toSet());

        List<DeskSearchUserDTO> deskSearchUserDTOS = userProvider.searchDeskUser(keyword).getData();

        if (checkTokenRangeSet.contains("all")) {
            // 能搜到所有用户
        } else if (checkTokenRangeSet.contains("QC")) {
            // 能搜到指定QC用户
            deskSearchUserDTOS.removeIf(
                    deskSearchUserDTO -> (!DemoUserConsts.QC_USERIDS.contains(deskSearchUserDTO.getId()))
            );
        } else if (checkTokenRangeSet.contains("PCOSdemo") && !checkTokenRangeSet.contains("all")) {
            //需要对搜索到的用户过滤
            deskSearchUserDTOS.removeIf(
                    deskSearchUserDTO -> (!DemoUserConsts.HEALTHY_USERIDS.contains(deskSearchUserDTO.getId())
                            && !DemoUserConsts.PCOS_USERIDS.contains(deskSearchUserDTO.getId()))
            );
        }

        List<UserSearchVO> userSearchVOS = new ArrayList<>();
        for (DeskSearchUserDTO deskSearchUserDTO : deskSearchUserDTOS) {
            UserSearchVO userSearchVO = new UserSearchVO();
            userSearchVO.setId(deskSearchUserDTO.getId());
            userSearchVO.setEmail(deskSearchUserDTO.getEmail());
            userSearchVO.setMobile(deskSearchUserDTO.getMobile());
            userSearchVO.setSn(deskSearchUserDTO.getSn());
            Integer status = deskSearchUserDTO.getStatus();
            Integer deleted = deskSearchUserDTO.getDeleted();
            userSearchVO.setStatus(status);
            Integer source = deskSearchUserDTO.getSource();
            Integer transferFlag = deskSearchUserDTO.getTransferFlag();
            Integer fresh = deskSearchUserDTO.getFresh();
            Integer turnFlag = null;
            if (deleted != 0) {
                turnFlag = -1;
            } else if (status != 1) {
                turnFlag = 0;
            } else if (fresh != 1) {
                turnFlag = 5;
            } else if (source == 0 && transferFlag == 0) {
                turnFlag = 1;
            } else if (source == 0 && transferFlag == 1) {
                turnFlag = 3;
            } else if ((source == 1 || source == 2 || source == 3) && transferFlag == 0) {
                turnFlag = 3;
            } else if (source == 4 || transferFlag == 2) {
                turnFlag = 4;
            }

            userSearchVO.setTurnFlag(turnFlag);
            userSearchVOS.add(userSearchVO);
        }
        return userSearchVOS;
    }

    @Override
    public UserInfoVO info(Long userId) {
        if (userId == null) {
            return null;
        }

        //adminLoginService.checkUserInfoPermission(userId);

        DeskUserInfoDTO deskUserInfoDTO = userProvider.getDeskUserInfoDTO(userId).getData();
        UserInfoVO appUserInfoVO = new UserInfoVO();
        BeanUtils.copyProperties(deskUserInfoDTO, appUserInfoVO);
        Integer status = deskUserInfoDTO.getStatus();
        Integer source = deskUserInfoDTO.getSource();
        Integer transferFlag = deskUserInfoDTO.getTransferFlag();
        Integer fresh = deskUserInfoDTO.getFresh();
        //0：未激活；1：v2用户未迁移；3：跳转到v3界面；4跳转到v4界面；5未完成注册流程的用户
        String sourceVo = null;
        if (status != 1) {
            sourceVo = "Not activated";
        } else if (fresh == 0) {
            sourceVo = "Unfinished registration process";
        } else if (source == 0 && transferFlag == 0) {
            sourceVo = "1.2 users not migrate";
        } else if (source == 0 && transferFlag == 1) {
            sourceVo = "1.2 users migrate to 1.3";
        } else if (transferFlag == 0 && (source == 1 || source == 2 || source == 3)) {
            sourceVo = "1.3 Registered users, not migrated to 1.4";
        } else if (source == 4 || transferFlag == 2) {
            sourceVo = "1.4 Users";
        }
        appUserInfoVO.setSource(sourceVo);


        String birthday = deskUserInfoDTO.getBirthYear() + "-" + deskUserInfoDTO.getBirthMonth() + "-" + deskUserInfoDTO.getBirthDay();
        appUserInfoVO.setBirthday(birthday);
        appUserInfoVO.setAge(AgeUtil.calculateAge(deskUserInfoDTO.getBirthYear(), deskUserInfoDTO.getBirthMonth(),
                deskUserInfoDTO.getBirthDay()));
        appUserInfoVO.setGoal(UserGoalEnum.get(deskUserInfoDTO.getGoalStatus()).getDesc());

        UserPeriodDTO userPeriodDTO = userProvider.getUserPeriod(userId).getData();
        String timeZone = userPeriodDTO.getTimeZone();
        long currentTimeMillis = System.currentTimeMillis();
        String dtfLocalTime = ZoneDateUtil.format(timeZone, currentTimeMillis, DatePatternConst.DATE_TIME_PATTERN);
        appUserInfoVO.setTimeZone(timeZone);
        appUserInfoVO.setLocalCurrentTime(dtfLocalTime);
        appUserInfoVO.setDiffTime(getDiffTime(timeZone));
        appUserInfoVO.setAvgLenCycle(appUserInfoVO.getAvgLenCycle());
        appUserInfoVO.setAvgLenPeriod(appUserInfoVO.getAvgLenPeriod());

        UserRecentAccessDTO userRecentAccess = accessLogProvider.getUserRecentAccess(userId).getData();
        if (userRecentAccess != null) {
            String os = userRecentAccess.getOs();
            String version = userRecentAccess.getVersion();
            String device = userRecentAccess.getDevice();
            String appVersion = userRecentAccess.getAppVersion();
            String networkType = userRecentAccess.getNetworkType();
            String info = os + "::" + version + "::" + device + "::" + appVersion + "::" + networkType;
            appUserInfoVO.setLastDivice(info);
        }

        String sn = appUserInfoVO.getSn();
        SnToRecordEntity snToRecordEntity = snToRecordDAO.getBySn(sn);
        if (!ObjectUtils.isEmpty(snToRecordEntity)) {
            appUserInfoVO.setDirection(snToRecordEntity.getDirection());
            appUserInfoVO.setBatchId(snToRecordEntity.getBatchId());
        }
        appUserInfoVO.setBirthday(appUserInfoVO.getBirthday());

        try {
            List<UserPatientDTO> userPatientDTOS = clinicProvider.listUserPatientDTOs(userId).getData();
            if (CollectionUtils.isNotEmpty(userPatientDTOS)) {
                UserPatientDTO userPatientDTO = userPatientDTOS.get(0);
                UserInfoVO.Patient patient = new UserInfoVO.Patient();
                patient.setTenantCode(userPatientDTO.getTenantCode());
                patient.setStatus(userPatientDTO.getStatus());
                appUserInfoVO.setPatient(patient);

                for (UserPatientDTO userPatient2 : userPatientDTOS) {
                    UserInfoVO.Patient patient2 = new UserInfoVO.Patient();
                    patient2.setTenantCode(userPatient2.getTenantCode());
                    patient2.setStatus(userPatient2.getStatus());
                    appUserInfoVO.getPatients().add(patient2);
                }

            }
        } catch (Exception e) {
            log.error("clinic server error", e);
        }

        if (StringUtils.isNotBlank(deskUserInfoDTO.getPartnerEmail())) {
            UserInfoVO.Partner partner = new UserInfoVO.Partner();
            partner.setPartnerEmail(deskUserInfoDTO.getPartnerEmail());
            partner.setPartnerStatus(deskUserInfoDTO.getPartnerStatus());
            appUserInfoVO.setPartner(partner);
        }

        Boolean inTestingScheduleExchange = userReminderComplaintDAO.inReminderComplaint(userId);
        appUserInfoVO.setInTestingScheduleExchange(inTestingScheduleExchange);

        return appUserInfoVO;
    }


    public static String getDiffTime(String timeZone) {
        long currentTimeMillis = System.currentTimeMillis();
        String dtfLocalTime = ZoneDateUtil.format(timeZone, currentTimeMillis, DatePatternConst.DATE_TIME_PATTERN);
        String baseLocalTime = ZoneDateUtil.format("Asia/Shanghai", currentTimeMillis, DatePatternConst.DATE_TIME_PATTERN);
        Date date = parseDate(dtfLocalTime, DatePatternConst.DATE_TIME_PATTERN);
        Date baseDate = parseDate(baseLocalTime, DatePatternConst.DATE_TIME_PATTERN);
        long betweenTimes = (date.getTime() - baseDate.getTime()) / 1000;
        String result;
        if (betweenTimes > 0) {
            result = "fast";
        } else if (betweenTimes < 0) {
            betweenTimes = -betweenTimes;
            result = "slow";
        } else {
            result = "the same to 'Asia/Shanghai'";
            log.info("result:" + result);
            return result;
        }
        long betweenHours = betweenTimes / 3600;
        long betweenMinutes = (betweenTimes - betweenHours * 3600) / 60;
        long betweenSeconds = betweenTimes - betweenHours * 3600 - betweenMinutes * 60;
        String hours = betweenHours < 10 ? ("0" + betweenHours) : String.valueOf(betweenHours);
        String minutes = betweenMinutes < 10 ? ("0" + betweenMinutes) : String.valueOf(betweenMinutes);
        String seconds = betweenSeconds < 10 ? ("0" + betweenSeconds) : String.valueOf(betweenSeconds);
        result = result + hours + ":" + minutes + ":" + seconds;
        log.info("result:" + result);
        return result;
    }

    /**
     * 将指定的日期字符串转换成日期
     *
     * @param dateStr 日期字符串
     * @param pattern 格式
     * @return 日期对象
     */
    public static Date parseDate(String dateStr, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        Date date;
        try {
            date = sdf.parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException("日期转化错误");
        }

        return date;
    }

    @Override
    public void resetPwd(Long userId) {
        if (userId == null) {
            throw new BackendException("param empty");
        }
        AppUserEntity user = appUserMapper.selectById(userId);
        //  t12345678
        user.setPassword("305ec48a4923c336d6cf0066574ee6a57c2b9443e9e9c10ad9e56aa04d6ccaef");
        user.setSalt("SWThW1S3kEZDyuuRFaih");
        appUserMapper.updateById(user);
        log.info("后台管理员：{}重置用户密码。用户:{}", ContextHolder.<BaseLoginInfo>getLoginInfo().getUsername(), userId);
    }

    @Override
    public void enable(Long userId) {
        if (userId == null) {
            throw new BackendException("param empty");
        }
        AppUserEntity user = appUserMapper.selectById(userId);
        user.setStatus(1);
        appUserMapper.updateById(user);
        log.info("后台管理员：{}激活用户。用户:{}", ContextHolder.<BaseLoginInfo>getLoginInfo().getUsername(), userId);
    }

    @Override
    public Long delete(Long userId) {
        if (userId == null) {
            throw new BackendException("param empty");
        }
        Long adminId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppUserEntity appUserEntity = appUserMapper.selectById(userId);
        String dfLocalTime = ZoneDateUtil.format("Asia/Shanghai", System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        appUserEntity.setEmail(appUserEntity.getEmail() + dfLocalTime + new Random().nextInt(1000));
        appUserEntity.setModifier(adminId);
        appUserEntity.setDeleted(1);
        UpdateEntityTimeUtil.updateBaseEntityTime("Asia/Shanghai", appUserEntity);
        appUserMapper.updateById(appUserEntity);
        log.info("后台管理员：{}删除用户。用户:{}", ContextHolder.<BaseLoginInfo>getLoginInfo().getUsername(), userId);
        //需要做退出登录处理
        String appMarkToken = cacheManager.getAppMarkToken(userId);
        if (StringUtils.isNotBlank(appMarkToken)) {
            authProvider.deleteToken(appMarkToken, UserTypeEnum.APP_USER.getType());
        }
        return userId;
    }

    @Override
    public Long modifyEmail(ModifyEmailParam modifyEmailParam) {
        Long userId = modifyEmailParam.getUserId();
        String oldEmail = modifyEmailParam.getOldEmail();
        String newEmail = modifyEmailParam.getNewEmail();
        AppUserEntity user = appUserMapper.selectById(userId);
        ssoProvider.changeUserEmail(userId, user.getTimeZone(), newEmail, "backend");
        log.info("后台管理员：{}修改用户email。用户:{},email从:{}变为:{}", ContextHolder.<BaseLoginInfo>getLoginInfo().getUsername(), userId, oldEmail, newEmail);
        //需要做退出登录处理
        String appMarkToken = cacheManager.getAppMarkToken(userId);
        if (StringUtils.isNotBlank(appMarkToken)) {
            authProvider.deleteToken(appMarkToken, UserTypeEnum.APP_USER.getType());
        }
        return modifyEmailParam.getUserId();
    }

    @Override
    public List<UserBindLogDTO> list10BindLog(String email, String code) {
        if (StringUtils.isBlank(code) || !"mira123".equals(code)) {
            throw new BackendException("没有权限");
        }
        AppUserDTO appUserDTO = userProvider.getUserByEmail(email).getData();
        //账号不存在
        if (appUserDTO == null) {
            throw new BackendException("not exist");
        }
        Long userId = appUserDTO.getId();
        List<UserBindLogDTO> bindLogDTOS = userProvider.list10BindLog(userId).getData();
        return bindLogDTOS;
    }

    @Override
    public void addTestingScheduleExchange(Long userId) {
        userReminderComplaintDAO.addTestingScheduleExchange(userId);
    }
}
