package com.mira.bluetooth.service.manager;

import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.bluetooth.dto.algorithm.response.AlgorithmReturnDTO;
import com.mira.api.bluetooth.dto.cycle.CycleAnalysisDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.bluetooth.dal.dao.AppUserAlgorithmResultDAO;
import com.mira.bluetooth.dal.entity.AppUserAlgorithmResultEntity;
import com.mira.core.util.JsonUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * 算法结果处理层
 *
 * <AUTHOR>
 */
@Component
public class AlgorithmResultManager {
    @Resource
    private AppUserAlgorithmResultDAO appUserAlgorithmResultDAO;

    @Transactional(rollbackFor = Exception.class)
    public AppUserAlgorithmResultEntity saveOrUpdateAlgorithmResult(Long userId, String timeZone, List<HormoneDTO> hormoneDTOS,
                                                              AlgorithmReturnDTO algorithmReturnDataDTO) {
        List<CycleDataDTO> cycleDataDTOS = algorithmReturnDataDTO.getCycle_data();
        String cycleDataStr = JsonUtil.toJson(cycleDataDTOS);
        Integer barTip = algorithmReturnDataDTO.getBar_tip();
        CycleAnalysisDTO cycleAnalysis = algorithmReturnDataDTO.getCycle_analysis();
        String cycleAnalysisStr = JsonUtil.toJson(cycleAnalysis);
        AlgorithmReturnDTO.ExtraResult extraResult = algorithmReturnDataDTO.getExtra_result();
        String extraResultStr = JsonUtil.toJson(extraResult);
        Integer thresholdMode = algorithmReturnDataDTO.getThreshold_mode();

        hormoneDTOS.sort((Comparator.comparing(HormoneDTO::getTest_time)));
        String hormoneDataStr = JsonUtil.toJson(hormoneDTOS);

        AppUserAlgorithmResultEntity userAlgorithmResultEntity = appUserAlgorithmResultDAO.getByUserId(userId);
        if (Objects.isNull(userAlgorithmResultEntity)) {
            userAlgorithmResultEntity = new AppUserAlgorithmResultEntity();
            userAlgorithmResultEntity.setUserId(userId);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, userAlgorithmResultEntity);
            userAlgorithmResultEntity.setCycleData(cycleDataStr);
            userAlgorithmResultEntity.setBarTip(barTip);
            userAlgorithmResultEntity.setCycleAnalysis(cycleAnalysisStr);
            userAlgorithmResultEntity.setExtraResult(extraResultStr);
            userAlgorithmResultEntity.setHormoneData(hormoneDataStr);
            userAlgorithmResultEntity.setThresholdMode(thresholdMode);
            appUserAlgorithmResultDAO.save(userAlgorithmResultEntity);
        } else {
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userAlgorithmResultEntity);
            userAlgorithmResultEntity.setCycleData(cycleDataStr);
            userAlgorithmResultEntity.setBarTip(barTip);
            userAlgorithmResultEntity.setCycleAnalysis(cycleAnalysisStr);
            userAlgorithmResultEntity.setExtraResult(extraResultStr);
            userAlgorithmResultEntity.setHormoneData(hormoneDataStr);
            userAlgorithmResultEntity.setThresholdMode(thresholdMode);
            appUserAlgorithmResultDAO.updateById(userAlgorithmResultEntity);
        }

        return userAlgorithmResultEntity;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAlgorithmThreshold(Long userId, String timeZone, Integer threshold) {
        AppUserAlgorithmResultEntity algorithmResultEntity = appUserAlgorithmResultDAO.getByUserId(userId);
        if (ObjectUtils.isNotEmpty(algorithmResultEntity)) {
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, algorithmResultEntity);
            algorithmResultEntity.setThresholdMode(threshold);
            appUserAlgorithmResultDAO.updateById(algorithmResultEntity);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAlgorithmCycleData(AppUserAlgorithmResultEntity algorithmResultEntity, String timeZone, List<CycleDataDTO> cycleData) {
        algorithmResultEntity.setCycleData(JsonUtil.toJson(cycleData));
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, algorithmResultEntity);
        appUserAlgorithmResultDAO.updateById(algorithmResultEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAlgorithmCycleDataForMenopause(Long userId, String timeZone,
                                                     List<CycleDataDTO> cycleData) {
        AppUserAlgorithmResultEntity algorithmResultEntity = appUserAlgorithmResultDAO.getByUserId(userId);
        if (ObjectUtils.isNotEmpty(algorithmResultEntity)) {
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, algorithmResultEntity);
            algorithmResultEntity.setCycleData(JsonUtil.toJson(cycleData));
            appUserAlgorithmResultDAO.updateById(algorithmResultEntity);
        }
    }
}
