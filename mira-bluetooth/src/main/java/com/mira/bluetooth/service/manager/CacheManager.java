package com.mira.bluetooth.service.manager;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.menopause.MenopauseResultDTO;
import com.mira.api.bluetooth.dto.tips.GetTipsReturnDTO;
import com.mira.api.message.dto.PushTokenDTO;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.dto.user.AppUserInfoDTO;
import com.mira.api.user.provider.IUserProvider;
import com.mira.bluetooth.dal.dao.AppUserAlgorithmResultDAO;
import com.mira.bluetooth.dal.entity.AppUserAlgorithmResultEntity;
import com.mira.bluetooth.properties.CacheExpireProperties;
import com.mira.redis.cache.RedisComponent;
import com.mira.redis.cache.StringRedisComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * Cache manager
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CacheManager {
    @Resource
    private RedisComponent redisComponent;
    @Resource
    private StringRedisComponent stringRedisComponent;

    @Resource
    private CacheExpireProperties cacheExpireProperties;

    @Resource
    private IUserProvider userProvider;

    @Resource
    private AppUserAlgorithmResultDAO appUserAlgorithmResultDAO;


    /**
     * 设备是否在数据上传白名单内
     *
     * @param userId 用户id
     * @return true/false
     */
    public Boolean isInDataUploadWhiteList(Long userId) {
        return stringRedisComponent.sIsmember(RedisCacheKeyConst.DATA_UPLOAD_WHITE, userId.toString());
    }

    /**
     * 设备是否受测试次数限制
     *
     * @param sn 设备序列号
     * @return true/false
     */
    public Boolean isRestrictDevice(String sn) {
        return redisComponent.exists(RedisCacheKeyConst.SYS_SN_LIMIT + sn);
    }

    /**
     * 缓存设备受测试次数限制
     *
     * @param sn         设备序列号
     * @param timeMillis 限制时间
     */
    public void cacheRestrictDevice(String sn, Long timeMillis) {
        redisComponent.setEx(RedisCacheKeyConst.SYS_SN_LIMIT + sn, "1", timeMillis, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取用户算法结果缓存
     *
     * @param userId 用户id
     * @return 算法结果
     */
    public AlgorithmResultDTO getAlgorithmResultCache(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_ALGORITHM_RESULT + userId;
        AlgorithmResultDTO algorithmResultDTO = null;

        try {
            algorithmResultDTO = redisComponent.get(cacheKey, AlgorithmResultDTO.class);
            if (algorithmResultDTO != null) {
                return algorithmResultDTO;
            }
        } catch (Exception e) {
            log.error("get algorithm result cache error", e);
        }

        AppUserAlgorithmResultEntity algorithmResultEntity = appUserAlgorithmResultDAO.getByUserId(userId);
        if (ObjectUtils.isNotEmpty(algorithmResultEntity)) {
            algorithmResultDTO = BeanUtil.toBean(algorithmResultEntity, AlgorithmResultDTO.class);
            redisComponent.setEx(cacheKey, algorithmResultDTO, cacheExpireProperties.getAlgorithmResult(), TimeUnit.MINUTES);
        }
        return algorithmResultDTO;
    }

    /**
     * 缓存用户算法结果
     *
     * @param userId             用户编号
     * @param algorithmResultDTO 算法表记录
     * @deprecated since 7.6.14
     */
    @Deprecated
    public void cacheAlgorithmResult(Long userId, AlgorithmResultDTO algorithmResultDTO) {
        String cacheKey = RedisCacheKeyConst.USER_ALGORITHM_RESULT + userId;
        redisComponent.setEx(cacheKey, algorithmResultDTO, cacheExpireProperties.getAlgorithmResult(), TimeUnit.MINUTES);
    }

    /**
     * 缓存用户算法结果
     *
     * @param userId                用户编号
     * @param algorithmResultEntity 算法结果
     */
    public void cacheAlgorithmResult(Long userId, AppUserAlgorithmResultEntity algorithmResultEntity) {
        String cacheKey = RedisCacheKeyConst.USER_ALGORITHM_RESULT + userId;
        if (ObjectUtils.isNotEmpty(algorithmResultEntity)) {
            AlgorithmResultDTO algorithmResultDTO = BeanUtil.toBean(algorithmResultEntity, AlgorithmResultDTO.class);
            redisComponent.setEx(cacheKey, algorithmResultDTO, cacheExpireProperties.getAlgorithmResult(), TimeUnit.MINUTES);
        }
    }

    /**
     * 删除用户算法结果缓存
     *
     * @param userId 用户编号
     * @return true/false
     */
    public Boolean deleteAlgorithmResult(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_ALGORITHM_RESULT + userId;
        return redisComponent.delete(cacheKey);
    }

    /**
     * 缓存tips算法结果
     *
     * @param userId           用户编号
     * @param getTipsReturnDTO tips算法结果
     */
    public void cacheTipsAlgorithmData(Long userId, GetTipsReturnDTO getTipsReturnDTO) {
        String cacheKey = RedisCacheKeyConst.SYS_TIPS_ALGORITHM_RESULT + userId;
        redisComponent.setEx(cacheKey, getTipsReturnDTO, cacheExpireProperties.getTipsResult(), TimeUnit.MINUTES);
    }

    /**
     * 删除Tips算法结果缓存
     *
     * @param userId 用户编号
     * @return true/false
     */
    public boolean deleteTipsAlgorithmResult(Long userId) {
        String cacheKey = RedisCacheKeyConst.SYS_TIPS_ALGORITHM_RESULT + userId;
        return redisComponent.delete(cacheKey);
    }

    /**
     * 获取更年期结果缓存
     *
     * @param userId 用户id
     * @return 更年期结果
     */
    public MenopauseResultDTO getMenopauseResultCache(Long userId) {
        String cacheKey = RedisCacheKeyConst.MENOPAUSE_RESULT + userId;
        MenopauseResultDTO menopauseResultDTO = redisComponent.get(cacheKey, MenopauseResultDTO.class);
        return menopauseResultDTO;
    }

    /**
     * 缓存更年期结果
     *
     * @param userId             用户编号
     * @param menopauseResultDTO 更年期结果
     */
    public void cacheMenopauseResult(Long userId, MenopauseResultDTO menopauseResultDTO) {
        String cacheKey = RedisCacheKeyConst.MENOPAUSE_RESULT + userId;
        redisComponent.setEx(cacheKey, menopauseResultDTO, cacheExpireProperties.getMenopauseResult(), TimeUnit.MINUTES);
    }

    public boolean deleteMenopauseResult(Long userId) {
        String cacheKey = RedisCacheKeyConst.MENOPAUSE_RESULT + userId;
        return redisComponent.delete(cacheKey);
    }

    /**
     * 获取用户的 push token
     *
     * @param userId 用户id
     * @return PushTokenDTO
     */
    public PushTokenDTO getPushToken(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_FIREBASE_TOKEN + userId;
        PushTokenDTO cache;
        try {
            cache = redisComponent.get(cacheKey, PushTokenDTO.class);
        } catch (Exception e) {
            log.error("get push token cache error", e);
            cache = null;
        }
        if (ObjectUtils.isNotEmpty(cache) && StringUtils.isNotBlank(cache.getPushToken())) {
            return cache;
        }

        AppUserInfoDTO appUserInfoDTO = userProvider.getUserInfoById(userId).getData();
        PushTokenDTO pushTokenDTO = new PushTokenDTO();
        pushTokenDTO.setPushToken(appUserInfoDTO.getPushToken());
        pushTokenDTO.setPlatform(appUserInfoDTO.getPlatform());
        redisComponent.setEx(cacheKey, pushTokenDTO, cacheExpireProperties.getPushToken(), TimeUnit.DAYS);

        return pushTokenDTO;
    }

    public boolean getMenopauseResultMark(Long userId) {
        String key = "lock:menopauseResult:" + userId;
        String value = "locked"; // 这里可以使用更复杂的值，例如包含唯一ID或时间戳
        // 尝试获取锁，此处设置锁的过期时间为1秒
        return redisComponent.setIfAbent(key, value, 1, TimeUnit.SECONDS);
    }

    public void deleteMenopauseResultMark(Long userId) {
        String key = "lock:menopauseResult:" + userId;
        redisComponent.delete(key);
    }
}
