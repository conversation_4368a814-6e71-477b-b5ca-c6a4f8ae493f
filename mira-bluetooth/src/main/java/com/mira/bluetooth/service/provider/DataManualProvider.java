package com.mira.bluetooth.service.provider;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mira.api.bluetooth.dto.backend.*;
import com.mira.api.bluetooth.dto.hormone.ManualDataDTO;
import com.mira.api.bluetooth.dto.hormone.UserDataManualDTO;
import com.mira.api.bluetooth.dto.hormone.UserDataManualPageRequestDTO;
import com.mira.api.bluetooth.dto.hormone.UserManualDataResponseDTO;
import com.mira.api.bluetooth.enums.BluetoothDataErrorEnum;
import com.mira.api.bluetooth.enums.BluetoothDataWarningEnum;
import com.mira.api.bluetooth.enums.RunBoardFlagEnum;
import com.mira.api.bluetooth.provider.IDataManualProvider;
import com.mira.api.message.dto.PushNotificationDTO;
import com.mira.api.message.dto.PushTokenDTO;
import com.mira.api.message.provider.IMessageProvider;
import com.mira.api.user.dto.user.AppUserDTO;
import com.mira.api.user.enums.UserEventEnum;
import com.mira.api.user.provider.IUserProvider;
import com.mira.bluetooth.dal.dao.WandsParamRecordDAO;
import com.mira.bluetooth.dal.entity.AppDataUploadEntity;
import com.mira.bluetooth.dal.entity.DataManualEntity;
import com.mira.bluetooth.dal.entity.WandsParamRecordEntity;
import com.mira.bluetooth.dal.mapper.AppDataUploadMapper;
import com.mira.bluetooth.dal.mapper.DataManualMapper;
import com.mira.bluetooth.exception.BluetoothException;
import com.mira.bluetooth.service.manager.CacheManager;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.response.CommonResult;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.redis.cache.RedisComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-08-14
 **/
@RestController
@Slf4j
public class DataManualProvider implements IDataManualProvider {
    @Resource
    private DataManualMapper dataManualMapper;
    @Resource
    private AppDataUploadMapper appDataUploadMapper;
    @Resource
    private IUserProvider userProvider;
    @Resource
    private IMessageProvider messageProvider;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private WandsParamRecordDAO wandsParamRecordDAO;

    @Resource
    private RedisComponent redisComponent;



    @Override
    public CommonResult<ManualDataResponseDTO> manualDataPage(DataManualPageRequestDTO dataManualPageRequestDTO) {
        ManualDataResponseDTO manualDataResponseDTO = new ManualDataResponseDTO();

        Page<DataManualEntity> page = dataManualMapper
                .dataPage(new Page<>(dataManualPageRequestDTO.getCurrent(), dataManualPageRequestDTO.getSize()), BeanUtil.beanToMap(dataManualPageRequestDTO, false, false));

        if (ObjectUtils.isEmpty(page) || page.getRecords().isEmpty()) {
            manualDataResponseDTO.setTotal(0);
            return CommonResult.OK(manualDataResponseDTO);
        }

        List<DataManualEntity> records = page.getRecords();

        Set<Long> userIds = records.stream()
                .map(DataManualEntity::getUserId)
                .collect(Collectors.toSet());
        Map<Long, String> emailMap = userProvider.listEmailByIds(userIds).getData();

        List<DataManualDTO> dataManualDTOS = new ArrayList<>();
        for (DataManualEntity dataManualEntity : records) {
            DataManualDTO dataManualDTO = new DataManualDTO();
            BeanUtils.copyProperties(dataManualEntity, dataManualDTO);
            String email = emailMap.get(dataManualEntity.getUserId());
            dataManualDTO.setEmail(email);
            dataManualDTOS.add(dataManualDTO);
        }

        manualDataResponseDTO.setTotal(page.getTotal());
        manualDataResponseDTO.setDataManualDTOS(dataManualDTOS);
        return CommonResult.OK(manualDataResponseDTO);
    }

    @Override
    public CommonResult<UserManualDataResponseDTO> userManualDataPage(UserDataManualPageRequestDTO userDataManualPageRequestDTO) {
        UserManualDataResponseDTO userManualDataResponseDTO = new UserManualDataResponseDTO();

        Page<DataManualEntity> page = dataManualMapper
                .userDataPage(new Page<>(userDataManualPageRequestDTO.getCurrent(),
                        userDataManualPageRequestDTO.getSize()), BeanUtil.beanToMap(userDataManualPageRequestDTO, false, false));

        if (ObjectUtils.isEmpty(page) || page.getRecords().isEmpty()) {
            userManualDataResponseDTO.setTotal(0);
            return CommonResult.OK(userManualDataResponseDTO);
        }

        List<DataManualEntity> records = page.getRecords();

        List<UserDataManualDTO> userDataManualDTOS = new ArrayList<>();
        for (DataManualEntity dataManualEntity : records) {
            UserDataManualDTO userDataManualDTO = new UserDataManualDTO();
            BeanUtils.copyProperties(dataManualEntity, userDataManualDTO);
            userDataManualDTOS.add(userDataManualDTO);
        }

        userManualDataResponseDTO.setTotal(page.getTotal());
        userManualDataResponseDTO.setUserDataManualDTOS(userDataManualDTOS);
        return CommonResult.OK(userManualDataResponseDTO);
    }

    @Override
    public CommonResult<DataManualDTO> detail(Long id, Long adminId) {
        DataManualEntity dataManualEntity = dataManualMapper.selectById(id);
        if (dataManualEntity.getDoubleCheck()==1){
            DataManualDTO dataManualDTO = new DataManualDTO();
            BeanUtils.copyProperties(dataManualEntity, dataManualDTO);
            return CommonResult.OK(dataManualDTO);
        }else {
            Integer status = dataManualEntity.getStatus();
            DataManualDTO dataManualDTO = new DataManualDTO();
            BeanUtils.copyProperties(dataManualEntity, dataManualDTO);
            dataManualEntity.setDoubleCheck(1);
            if (status==6){
                dataManualEntity.setModifier(adminId);
                dataManualEntity.setModifyTime(System.currentTimeMillis());
                dataManualEntity.setModifyTimeStr(ZoneDateUtil.format(dataManualEntity.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN));
            }
            dataManualMapper.updateById(dataManualEntity);
            return CommonResult.OK(dataManualDTO);
        }

    }

    @Override
    public CommonResult<Integer> audit(DataManualAuditDTO dataManualAuditDTO, Long adminId) {
        Integer returnStatus = 2;
        DataManualEntity dataManualEntity = dataManualMapper.selectById(dataManualAuditDTO.getId());
        Long userId = dataManualEntity.getUserId();
        Long completeTimestamp = dataManualEntity.getCompleteTimestamp();
        String testWandType = dataManualEntity.getTestWandType();
        BigDecimal t1ConValue = dataManualAuditDTO.getT1ConValue();
        BigDecimal t2ConValue = dataManualAuditDTO.getT2ConValue();
        BigDecimal t3ConValue = dataManualAuditDTO.getT3ConValue();
        // edit dataManualEntity
        dataManualEntity.setT1ConValue(t1ConValue);
        dataManualEntity.setT2ConValue(t2ConValue);
        dataManualEntity.setT3ConValue(t3ConValue);
        dataManualEntity.setData1Compare(dataManualAuditDTO.getData1Compare());
        dataManualEntity.setData2Compare(dataManualAuditDTO.getData2Compare());
        dataManualEntity.setData3Compare(dataManualAuditDTO.getData3Compare());
        UpdateEntityTimeUtil.updateBaseEntityTime(dataManualEntity.getTimeZone(), dataManualEntity);
        // build and save dataUploadEntity
        QueryWrapper<AppDataUploadEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("test_wand_type", testWandType);
        queryWrapper.eq("error", "00");
        queryWrapper.eq("warning", "00");
        queryWrapper.orderByDesc("complete_timestamp");
        List<AppDataUploadEntity> hisDataEntities = appDataUploadMapper.selectList(queryWrapper);
        if (!hisDataEntities.isEmpty()) {
            AppDataUploadEntity recentAppDataUploadEntity = hisDataEntities.get(0);
            returnStatus = getReturnStatus(completeTimestamp, testWandType, t1ConValue, t2ConValue, recentAppDataUploadEntity);
            if (returnStatus == -1) {
                throw new BluetoothException("function not implemented yet with test wand type: " + testWandType);
            }
        }
        if (returnStatus == 2) {
            AppDataUploadEntity dataUploadEntity = buildManualDataUpload(dataManualEntity, hisDataEntities);
            appDataUploadMapper.insert(dataUploadEntity);
            // edit period
            userProvider.systemEditPeriod(userId);
        }
        // change status
        dataManualEntity.setStatus(returnStatus);
        dataManualEntity.setModifier(adminId);
        dataManualMapper.updateById(dataManualEntity);
        return CommonResult.OK(returnStatus);
    }


    private AppDataUploadEntity buildManualDataUpload(DataManualEntity dataManualEntity, List<AppDataUploadEntity> hisDataEntities) {
        Long userId = dataManualEntity.getUserId();
        String testWandType = dataManualEntity.getTestWandType();
        BigDecimal t1ConValue = dataManualEntity.getT1ConValue();
        BigDecimal t2ConValue = dataManualEntity.getT2ConValue();
        BigDecimal t3ConValue = dataManualEntity.getT3ConValue();
        Integer data1Compare = dataManualEntity.getData1Compare();
        Integer data2Compare = dataManualEntity.getData2Compare();
        Integer data3Compare = dataManualEntity.getData3Compare();
        String timeZone = dataManualEntity.getTimeZone();
        String completeTime = dataManualEntity.getCompleteTime();
        Long completeTimestamp = dataManualEntity.getCompleteTimestamp();
        // get testWandBatch
        String testWandBatch = this.getTestWandType(testWandType, hisDataEntities);
        // get t WarningCode

        AppDataUploadEntity dataUploadEntity = new AppDataUploadEntity();
        dataUploadEntity.setAutoFlag(1);
        dataUploadEntity.setError("00");
        dataUploadEntity.setWarning("00");
        dataUploadEntity.setUserId(userId);
        dataUploadEntity.setTimeZone(timeZone);
        dataUploadEntity.setTestWandType(testWandType);
        dataUploadEntity.setTestWandBatch(testWandBatch);
        dataUploadEntity.setSource(2);
        dataUploadEntity.setRawResultData("-2");
        dataUploadEntity.setCompleteTime(completeTime);
        dataUploadEntity.setCompleteTimestamp(completeTimestamp);
        dataUploadEntity.setResultVersionFormat("03");

        dataUploadEntity.setT1ConValue(t1ConValue);
        dataUploadEntity.setT2ConValue(t2ConValue);
        dataUploadEntity.setT3ConValue(t3ConValue);
        buildTWarningCode(dataUploadEntity, data1Compare, data2Compare, data3Compare);

        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, dataUploadEntity);

        dataUploadEntity.setRunBoardFlag(RunBoardFlagEnum.MANUAL_ADD_DATA.getCode());
        return dataUploadEntity;
    }

    private void buildTWarningCode(AppDataUploadEntity dataUploadEntity, Integer data1Compare, Integer data2Compare, Integer data3Compare) {
        String testWandType = dataUploadEntity.getTestWandType();
        String testWandBatch = dataUploadEntity.getTestWandBatch();
        dataUploadEntity.setT1WarningCode("00");
        dataUploadEntity.setT2WarningCode("00");
        dataUploadEntity.setT3WarningCode("00");
        if (data1Compare == null || data2Compare == null || data3Compare == null) {
            return;
        }
        if (data1Compare == 0 && data2Compare == 0 & data3Compare == 0) {
            return;
        }
        if (testWandType.equals(0 + WandTypeEnum.LH.getString())) {

        } else if (testWandType.equals(0 + WandTypeEnum.E3G_LH.getString())) {

        } else if (testWandType.equals(0 + WandTypeEnum.PDG.getString())) {

        }

    }

    private String getTestWandType(String testWandType, List<AppDataUploadEntity> hisDataEntities) {
        String testWandBatch;
        if (!hisDataEntities.isEmpty()) {
            // 取最近一条同类型测试数据的试剂批次
            List<AppDataUploadEntity> sortedUploadEntityList = hisDataEntities.stream()
                    .sorted(Comparator.comparing(AppDataUploadEntity::getCompleteTimestamp, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
            testWandBatch = sortedUploadEntityList.get(0).getTestWandBatch();
        } else {
            // 从工厂数据库获取批次
            String uStripType = testWandType;
            if (uStripType.startsWith("0")) {
                uStripType = uStripType.substring(1);
            }
            List<WandsParamRecordEntity> wandsParamRecordEntities = wandsParamRecordDAO.listByUStripTypeOrderByWandBatchLimitTime(uStripType);
            testWandBatch = wandsParamRecordEntities.get(0).getWandBatch3();
        }
        return testWandBatch;
    }

    private Integer getReturnStatus(Long completeTimestamp, String testWandType, BigDecimal t1ConValue, BigDecimal t2ConValue, AppDataUploadEntity recentDataUploadEntity) {
        Integer statusFlag = 2;
        Long recentCompleteTimestamp = recentDataUploadEntity.getCompleteTimestamp();
        if (recentCompleteTimestamp == null) {
            recentCompleteTimestamp = ZoneDateUtil.timestamp(recentDataUploadEntity.getTimeZone(), recentDataUploadEntity.getCompleteTime(), DatePatternConst.DATE_TIME_PATTERN);
        }
        String recentTestWandType = recentDataUploadEntity.getTestWandType();
        if (Math.abs(recentCompleteTimestamp - completeTimestamp) < 16 * 60 * 1000) {
            // 15分钟之内有有效数据，并且数值相等，changeStatus-->3 return
            // 15分钟之内有有效数据，并且数值不等，但是试剂类型相同，changeStatus-->4 return
            if (testWandType.equals(0 + WandTypeEnum.LH.getString())) {
                BigDecimal lhValue = recentDataUploadEntity.getT1ConValue();
                if (t1ConValue.subtract(lhValue).floatValue() < 0.5) {
                    statusFlag = 3;
                } else if (recentTestWandType.equals(testWandType)) {
                    statusFlag = 4;
                }
            } else if (testWandType.equals(0 + WandTypeEnum.E3G_LH.getString())) {
                BigDecimal e3gValue = recentDataUploadEntity.getT1ConValue();
                BigDecimal lhValue = recentDataUploadEntity.getT2ConValue();
                if (t1ConValue.subtract(e3gValue).floatValue() < 0.5 && t2ConValue.subtract(lhValue).floatValue() < 0.5) {
                    statusFlag = 3;
                } else if (recentTestWandType.equals(testWandType)) {
                    statusFlag = 4;
                }
            } else if (testWandType.equals(0 + WandTypeEnum.PDG.getString())) {
                BigDecimal pdgConValue = recentDataUploadEntity.getT1ConValue();
                if (t1ConValue.subtract(pdgConValue).floatValue() < 0.5) {
                    statusFlag = 3;
                } else if (recentTestWandType.equals(testWandType)) {
                    statusFlag = 4;
                }
            } else {
                statusFlag = -1;
            }
        }
        return statusFlag;
    }

    @Override
    public CommonResult<List<BackendWandsParamRecordDTO>> wandBatchList(String testWandType) {
        List<BackendWandsParamRecordDTO> wandsParamRecordDTOS = new ArrayList<>();
        String uStripType = "";
        if (testWandType.equals("0" + WandTypeEnum.LH.getString())) {
            uStripType = WandTypeEnum.LH.getString();
        } else if (testWandType.equals("0" + WandTypeEnum.E3G_LH.getString())) {
            uStripType = WandTypeEnum.E3G_LH.getString();
        } else if (testWandType.equals("0" + WandTypeEnum.PDG.getString())) {
            uStripType = WandTypeEnum.PDG.getString();
        } else if (testWandType.equals(WandTypeEnum.LH_E3G_PDG.getString())) {
            uStripType = WandTypeEnum.LH_E3G_PDG.getString();
        }
        if (StringUtils.isBlank(uStripType)) {
            return CommonResult.OK(wandsParamRecordDTOS);
        }
        List<WandsParamRecordEntity> wandsParamRecordEntities = wandsParamRecordDAO.listByUStripTypeOrderByWandBatchLimitTime(uStripType);
        for (WandsParamRecordEntity wandsParamRecordEntity : wandsParamRecordEntities) {
            BackendWandsParamRecordDTO wandsParamRecordVO = new BackendWandsParamRecordDTO();
            BeanUtils.copyProperties(wandsParamRecordEntity, wandsParamRecordVO);
            wandsParamRecordVO.setTestWandBatch(wandsParamRecordEntity.getWandBatch3());
            wandsParamRecordDTOS.add(wandsParamRecordVO);
        }
        return CommonResult.OK(wandsParamRecordDTOS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Void> addTestData(List<ManualAddTestDataDTO> testDataParamList) {
        DataManualProvider dataManualProviderProxy = (DataManualProvider) AopContext.currentProxy();
        for (ManualAddTestDataDTO testDataParam : testDataParamList) {
            dataManualProviderProxy.saveTestData(testDataParam);
        }
        return CommonResult.OK();
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveTestData(ManualAddTestDataDTO testDataParam) {
        // 获取用户信息
        AppUserDTO appUser = userProvider.getUserByEmail(testDataParam.getEmail()).getData();
        if (Objects.isNull(appUser)) {
            throw new BluetoothException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "用户信息没有找到！");
        }

        // 获取用户上传数据
        AppDataUploadEntity dataUploadEntity =
                appDataUploadMapper.selectOne(Wrappers.<AppDataUploadEntity>lambdaQuery()
                        .eq(AppDataUploadEntity::getUserId, appUser.getId())
                        .eq(AppDataUploadEntity::getTestWandType, testDataParam.getTestWandType())
                        .eq(AppDataUploadEntity::getError, BluetoothDataErrorEnum.CODE_0.getValue())
                        .eq(AppDataUploadEntity::getT1ErrorCode, BluetoothDataErrorEnum.CODE_0.getValue())
                        .eq(AppDataUploadEntity::getT2ErrorCode, BluetoothDataErrorEnum.CODE_0.getValue())
                        .eq(AppDataUploadEntity::getT3ErrorCode, BluetoothDataErrorEnum.CODE_0.getValue())
                        .eq(AppDataUploadEntity::getWarning, BluetoothDataWarningEnum.CODE_0.getValue())
                        .eq(AppDataUploadEntity::getT1WarningCode, BluetoothDataWarningEnum.CODE_0.getValue())
                        .eq(AppDataUploadEntity::getT2WarningCode, BluetoothDataWarningEnum.CODE_0.getValue())
                        .eq(AppDataUploadEntity::getT3WarningCode, BluetoothDataWarningEnum.CODE_0.getValue())
                        .orderByDesc(AppDataUploadEntity::getCompleteTime)
                        .last("limit 1"));

        // 取一条同类型、数据完整且无报错信息的最新记录
        AppDataUploadEntity recentAppDataUploadEntity = new AppDataUploadEntity();
        if (Objects.nonNull(dataUploadEntity)) {
            BeanUtil.copyProperties(dataUploadEntity, recentAppDataUploadEntity);
        } else {
            // 取其他用户的同时区、同类型、数据完整且无报错信息的记录，autoFlag设置为手动上传
            AppDataUploadEntity otherUserDataUpload =
                    appDataUploadMapper.selectOne(Wrappers.<AppDataUploadEntity>lambdaQuery()
                            .eq(AppDataUploadEntity::getTimeZone, appUser.getTimeZone())
                            .eq(AppDataUploadEntity::getTestWandType, testDataParam.getTestWandType())
                            .eq(AppDataUploadEntity::getError, BluetoothDataErrorEnum.CODE_0.getValue())
                            .eq(AppDataUploadEntity::getT1ErrorCode, BluetoothDataErrorEnum.CODE_0.getValue())
                            .eq(AppDataUploadEntity::getT2ErrorCode, BluetoothDataErrorEnum.CODE_0.getValue())
                            .eq(AppDataUploadEntity::getT3ErrorCode, BluetoothDataErrorEnum.CODE_0.getValue())
                            .eq(AppDataUploadEntity::getWarning, BluetoothDataWarningEnum.CODE_0.getValue())
                            .eq(AppDataUploadEntity::getT1WarningCode, BluetoothDataWarningEnum.CODE_0.getValue())
                            .eq(AppDataUploadEntity::getT2WarningCode, BluetoothDataWarningEnum.CODE_0.getValue())
                            .eq(AppDataUploadEntity::getT3WarningCode, BluetoothDataWarningEnum.CODE_0.getValue())
                            .orderByDesc(AppDataUploadEntity::getId)
                            .last("limit 1"));
            if (Objects.isNull(otherUserDataUpload)) {
                throw new BluetoothException("没有找到相同试剂类型的正常测试数据，无法添加测试数据！");
            }
            BeanUtil.copyProperties(otherUserDataUpload, recentAppDataUploadEntity);
            recentAppDataUploadEntity.setUserId(appUser.getId());
        }

        // 更新浓度值
        recentAppDataUploadEntity.setAutoFlag(1);
        recentAppDataUploadEntity.setRunBoardFlag(RunBoardFlagEnum.MANUAL_ADD_DATA.getCode());
        recentAppDataUploadEntity.setId(null);
        recentAppDataUploadEntity.setT1ConValue(testDataParam.getT1());
        recentAppDataUploadEntity.setT2ConValue(testDataParam.getT2());
        recentAppDataUploadEntity.setT3ConValue(testDataParam.getT3());
        // 更新时间字段
        recentAppDataUploadEntity.setCompleteTime(testDataParam.getCompleteTime());
        recentAppDataUploadEntity.setFirstTimeStamp(testDataParam.getCompleteTime());
        recentAppDataUploadEntity.setCompleteTimestamp(getTimestampByTimeZone(testDataParam.getCompleteTime(), recentAppDataUploadEntity.getTimeZone()));
        recentAppDataUploadEntity.setCreateTime(recentAppDataUploadEntity.getCompleteTimestamp());
        recentAppDataUploadEntity.setCreateTimeStr(testDataParam.getCompleteTime());
        recentAppDataUploadEntity.setModifyTime(recentAppDataUploadEntity.getCompleteTimestamp());
        recentAppDataUploadEntity.setModifyTimeStr(testDataParam.getCompleteTime());
        recentAppDataUploadEntity.setSysNote("手动添加测试数据");
        // 新增测试记录
        appDataUploadMapper.insert(recentAppDataUploadEntity);

        // 编辑经期
        userProvider.systemEditPeriod(appUser.getId());
    }

    private Long getTimestampByTimeZone(String time, String timeZone) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone(timeZone));
        Date date = null;
        try {
            date = sdf.parse(time);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        return date.getTime();
    }

    @Override
    public CommonResult<Void> ignore(Long id) {
        DataManualEntity dataManualEntity = dataManualMapper.selectById(id);
        dataManualEntity.setStatus(5);
        dataManualMapper.updateById(dataManualEntity);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<Void> sendNotification(Long id, Long notificationDefineId) {
        DataManualEntity dataManualEntity = dataManualMapper.selectById(id);
        Long userId = dataManualEntity.getUserId();

        PushTokenDTO pushToken = cacheManager.getPushToken(userId);
        if (StringUtils.isBlank(pushToken.getPushToken())) {
            log.error("sendNotification defineId:{}, user:{} push token is empty", notificationDefineId, userId);
            return CommonResult.OK();
        }
        messageProvider.sendNotification(new PushNotificationDTO()
                .setUserId(userId)
                .setTimeZone("Asia/Shanghai")
                .setPlatform(pushToken.getPlatform())
                .setDefineId(notificationDefineId)
                .setPushFirebase(Boolean.TRUE)
                .setSaveRecord(Boolean.TRUE)
                .setTokens(Collections.singletonList(pushToken.getPushToken())));

        dataManualEntity.setNotificationStatus(notificationDefineId);
        dataManualMapper.updateById(dataManualEntity);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<Void> addHormoneData(ManualDataDTO manualDataDTO, Long userId, String timeZone) {
        String completeTime = manualDataDTO.getCompleteTime().trim();
        String testWandType = manualDataDTO.getTestWandType();
        DataManualEntity dataManualEntity = new DataManualEntity();
        dataManualEntity.setUserId(userId);
        dataManualEntity.setTestWandType(testWandType);
        dataManualEntity.setStatus(0);
        dataManualEntity.setT1ConValue(manualDataDTO.getT1ConValue());
        dataManualEntity.setT2ConValue(manualDataDTO.getT2ConValue());
        dataManualEntity.setT3ConValue(manualDataDTO.getT3ConValue());
        dataManualEntity.setPhotoUrl1(manualDataDTO.getPhotoUrl1());
        dataManualEntity.setPhotoUrl2(manualDataDTO.getPhotoUrl2());
        dataManualEntity.setPhotoUrl3(manualDataDTO.getPhotoUrl3());
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, dataManualEntity);
        //如果同一个用户，出现同样的完成时间，就随机增加几秒
        //        List<DataManualEntity> appDataManualEntities = appDataManualDAO.listByUserIdAndCompleteTime(userId, completeTime);
        //        if (CollectionUtils.isNotEmpty(appDataManualEntities)) {
        //            //对字符串时间随机增加1s~10s
        //            completeTime = LocalDateUtil.addRandomSeconds(completeTime);
        //        }
        dataManualEntity.setCompleteTime(completeTime);
        Long completeTimestamp = ZoneDateUtil.timestamp(timeZone, completeTime, DatePatternConst.DATE_TIME_PATTERN);
        dataManualEntity.setCompleteTimestamp(completeTimestamp);
        dataManualEntity.setCreator(userId);
        dataManualEntity.setModifier(-1L);
        dataManualMapper.insert(dataManualEntity);

        publishDelayedTask(userId,testWandType,dataManualEntity);

        return CommonResult.OK();
    }

    /**
     * 将未执行的手动添加数据task，添加到执行队列
     */
    @Override
    public CommonResult<Integer> addUnprocessManualData2Task(Integer count) {
        List<DataManualEntity> dataManualEntities = dataManualMapper.selectList(
                new QueryWrapper<DataManualEntity>()
                        .eq("status", 0)
                        .orderByDesc("create_time")
                        .last("limit "+count));
        for (DataManualEntity dataManualEntity : dataManualEntities) {
            publishDelayedTask(dataManualEntity.getUserId(),dataManualEntity.getTestWandType(),dataManualEntity);
        }

        return CommonResult.OK(dataManualEntities.size());
    }

    public void publishDelayedTask(Long userId, String testWandType, DataManualEntity dataManualEntity) {
        // pub event
        String eventId = UUID.randomUUID().toString();
        Map<String, String> event = new HashMap<>();
        event.put("userId", userId.toString());
        event.put("testWandType", testWandType);
        event.put("completeTime", dataManualEntity.getCompleteTime());
        event.put("dataManualJson", JsonUtil.toJson(dataManualEntity));

        redisComponent.publish("add_delayed_task", JsonUtil.toJson(event));
        log.info("手动添加数据：publish addDelayedTask, user_id:{}, testWandType:{}, completeTime:{}",
                userId, testWandType, dataManualEntity.getCompleteTime());

    }

    @Override
    public CommonResult<Void> addDelayedTaskHormoneData(Long userId, String testWandType, String completeTime, String dataManualJson) {
        DataManualEntity dataManualEntity = JsonUtil.toObject(dataManualJson, DataManualEntity.class);
        //再查询一次，看下是否已经处理过
        DataManualEntity dbDataManualEntity = dataManualMapper.selectById(dataManualEntity.getId());
        Integer dbStatus = dbDataManualEntity.getStatus();
        if (dbStatus != 0) {
            log.error("手动添加数据：addDelayedTaskHormoneData error userId:{},completeTime:{}, dbStatus:{}", userId,completeTime, dbStatus);
            return CommonResult.OK();
        }

        // build and save dataUploadEntity
        QueryWrapper<AppDataUploadEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("test_wand_type", testWandType);
        queryWrapper.eq("error", "00");
        queryWrapper.eq("warning", "00");
        queryWrapper.orderByDesc("complete_timestamp");
        List<AppDataUploadEntity> hisDataEntities = appDataUploadMapper.selectList(queryWrapper);

        AppDataUploadEntity dataUploadEntity = buildManualDataUpload(dataManualEntity, hisDataEntities);
        appDataUploadMapper.insert(dataUploadEntity);
        //  状态改为系统延迟一段时间后自动添加成功
        dataManualEntity.setStatus(6);
        dataManualEntity.setModifier(-1L);
        dataManualEntity.setModifyTime(System.currentTimeMillis());
        dataManualEntity.setModifyTimeStr(ZoneDateUtil.format(dbDataManualEntity.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN));
        dataManualMapper.updateById(dataManualEntity);

        // edit period
        userProvider.systemEditPeriod(userId);

        //send notification
        this.sendNotification(dataManualEntity.getId(), 29L);
        return CommonResult.OK();
    }
}
