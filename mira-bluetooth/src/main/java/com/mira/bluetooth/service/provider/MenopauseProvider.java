package com.mira.bluetooth.service.provider;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.menopause.MenopauseResultDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.provider.IMenopauseProvider;
import com.mira.bluetooth.dal.dao.AppUserMenopauseResultDAO;
import com.mira.bluetooth.dal.entity.AppUserMenopauseResultEntity;
import com.mira.bluetooth.dto.algorithm.request.NewHormoneDataRequest;
import com.mira.bluetooth.dto.algorithm.response.AlgorithmMenopauseResultDTO;
import com.mira.bluetooth.service.manager.AlgorithmInvokeManager;
import com.mira.bluetooth.service.manager.AlgorithmResultManager;
import com.mira.bluetooth.service.manager.CacheManager;
import com.mira.core.response.CommonResult;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-07-17
 **/
@RestController
public class MenopauseProvider implements IMenopauseProvider {
    @Resource
    private CacheManager cacheManager;
    @Resource
    private AppUserMenopauseResultDAO userMenopauseResultDAO;
    @Resource
    private AlgorithmInvokeManager algorithmInvokeManager;
    @Resource
    private AlgorithmResultManager algorithmResultManager;


    @Override
    public CommonResult<MenopauseResultDTO> getMenopauseResult(Long userId, AlgorithmRequestTypeEnum algorithmRequestTypeEnum) {
        //从缓存中获取更年期结果
        MenopauseResultDTO menopauseResultDTO = cacheManager.getMenopauseResultCache(userId);
        if (menopauseResultDTO != null) {
            return CommonResult.OK(menopauseResultDTO);
        }
        //什么时候刷新缓存？
        NewHormoneDataRequest newHormoneDataRequest = algorithmInvokeManager.buildGetMenopauseResultRequest(userId);
        String timeZone = newHormoneDataRequest.getTimeZone();
        AlgorithmMenopauseResultDTO algorithmMenopauseResultDTO = algorithmInvokeManager.callGetMenopauseResult(newHormoneDataRequest, algorithmRequestTypeEnum);
        if (algorithmMenopauseResultDTO != null) {
            menopauseResultDTO = new MenopauseResultDTO();
            menopauseResultDTO.setDefineStage(algorithmMenopauseResultDTO.getTracking_menopause_define_stage());
            menopauseResultDTO.setBackendStatus(algorithmMenopauseResultDTO.getTracking_menopause_backend_status());
            menopauseResultDTO.setDefineStageDate(algorithmMenopauseResultDTO.getTracking_menopause_define_stage_date());
            menopauseResultDTO.setProgressStatus(algorithmMenopauseResultDTO.getTracking_menopause_progress_status());
            menopauseResultDTO.setTestPlanStartDate(algorithmMenopauseResultDTO.getMenopause_test_plan_start_date());
            menopauseResultDTO.setTestPlanEndDate(algorithmMenopauseResultDTO.getMenopause_test_plan_end_date());
            menopauseResultDTO.setTestPlanFirstStartDate(algorithmMenopauseResultDTO.getMenopause_test_plan_first_start_date());
            menopauseResultDTO.setTestPlanFirstEndDate(algorithmMenopauseResultDTO.getMenopause_test_plan_first_end_date());
            menopauseResultDTO.setTestPlanSecondStartDate(algorithmMenopauseResultDTO.getMenopause_test_plan_second_start_date());
            menopauseResultDTO.setTestPlanSecondEndDate(algorithmMenopauseResultDTO.getMenopause_test_plan_second_end_date());
            menopauseResultDTO.setStatusRollBack(algorithmMenopauseResultDTO.getMenopause_status_roll_back());
            
            if (cacheManager.getMenopauseResultMark(userId)) {
                try {
                    AppUserMenopauseResultEntity userMenopauseResultEntity = userMenopauseResultDAO.getByUserId(userId);
                    if (userMenopauseResultEntity == null) {
                        userMenopauseResultEntity = new AppUserMenopauseResultEntity();
                        userMenopauseResultEntity.setUserId(userId);
                        BeanUtil.copyProperties(menopauseResultDTO, userMenopauseResultEntity);
                        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, userMenopauseResultEntity);
                        userMenopauseResultDAO.save(userMenopauseResultEntity);
                    } else {
                        BeanUtil.copyProperties(menopauseResultDTO, userMenopauseResultEntity);
                        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userMenopauseResultEntity);
                        userMenopauseResultDAO.updateById(userMenopauseResultEntity);
                    }
                } finally {
                    cacheManager.deleteMenopauseResultMark(userId);
                }
            }
        }
        cacheManager.cacheMenopauseResult(userId, menopauseResultDTO);

        List<CycleDataDTO> cycleData = algorithmMenopauseResultDTO.getCycle_data();
        algorithmResultManager.updateAlgorithmCycleDataForMenopause(userId, timeZone, cycleData);
        // delete cache
        cacheManager.deleteAlgorithmResult(userId);
        cacheManager.deleteTipsAlgorithmResult(userId);

        return CommonResult.OK(menopauseResultDTO);
    }


    @Override
    public CommonResult<Integer> getAfterCompletedRemind(Long userId) {
        AppUserMenopauseResultEntity userMenopauseResultEntity = userMenopauseResultDAO.getByUserId(userId);
        Integer afterCompletedRemind = 0;
        if (userMenopauseResultEntity != null) {
            afterCompletedRemind = userMenopauseResultEntity.getAfterCompletedRemind();
        }
        return CommonResult.OK(afterCompletedRemind);
    }

    @Override
    public CommonResult<Void> setAfterCompletedRemind(Long userId, Integer remind) {
        AppUserMenopauseResultEntity userMenopauseResultEntity = userMenopauseResultDAO.getByUserId(userId);
        if (userMenopauseResultEntity != null) {
            userMenopauseResultEntity.setAfterCompletedRemind(remind);
            UpdateEntityTimeUtil.updateBaseEntityTime(userMenopauseResultEntity.getTimeZone(), userMenopauseResultEntity);
            userMenopauseResultDAO.updateById(userMenopauseResultEntity);
        }
        return CommonResult.OK();
    }
}
