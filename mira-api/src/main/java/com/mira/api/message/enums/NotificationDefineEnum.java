package com.mira.api.message.enums;

import lombok.Getter;

/**
 * 通知定义枚举
 *
 * <AUTHOR>
 */
@Getter
public enum NotificationDefineEnum {
    // 测试日REMINDER
    TESTING_DAY_REMINDER_E3G(1L, "Test reminder: Mira Fertility Plus Wand (LH+E3G)"),
    TESTING_DAY_REMINDER_PDG(2L, "Test reminder: Mira Fertility Confirm Wand (PdG)"),
    TESTING_DAY_REMINDER_HCG(3L, "Test reminder: Mira Pregnancy Plus Wand (hCG)"),
    TESTING_DAY_REMINDER_FSH(49L, "Test reminder: Mira Ovum Wand (FSH)"),
    TESTING_DAY_REMINDER_HCG_QUALITATIVE(55L, "Test reminder: Mira Pregnancy Wand(hCG qualitative)"),
    TESTING_DAY_REMINDER_MAX(59L, "It’s testing time for LH, PdG and E3G!"),
    TESTING_DAY_REMINDER_MAX_OVUM(385L, "It’s testing time for LH, PdG, E3G, and FSH!"),

    TESTING_DAY_REMINDER_MAX_TOMORROW(386L, "Test your LH, PdG and E3G tomorrow"),
    TESTING_DAY_REMINDER_MAX_OVUM_TOMORROW(387L, "Test your LH, PdG, E3G and FSH tomorrow"),
    TESTING_DAY_REMINDER_OVUM_TOMORROW(388L, "Test your FSH tomorrow"),

    // return to app
    WE_MISS_YOU(13L, "It looks like you haven’t recorded anything into the app in last 7 days. For best results, remember to stay consistent with testing and record your data on the Mira App."),
    RETURN_TO_APP(14L, "Hey! It seems like you haven’t recorded any data in last 30 days. Without this information, Mira can’t give you accurate predictions about your cycle. If you’re having trouble using the Mira App, please let us know how we can improve."),
    HELP_MIRA_GET_BETTER(15L, "You haven’t recorded anything in the last 60 days. Would you answer a few questions to let us know how we can improve?"),

    // test reminder
    TEST_REMINDER_LH_SURGE_WITH_MAX(62L, "push right now after test: LH surge and test again tonight between 6 and 10 PM"),
    TEST_REMINDER_LH_SURGE_AGAIN_WITH_MAX(63L, "test again with Mira Fertility MAX Wands to confirm your LH peak today."),
    PDG_FIRST_TEST(64L, "some information about article"),

    // warning
    TOO_MANY_TEST_DATA(65L, "You have too many tests today."),
    UNSUPPORTED_TEST_WAND(82L, "The test wand is currently not supported. Please contact us!"),

    // manual add data
    YOUR_DATA_WAS_ADDED(29L, "Your data was added"),
    YOUR_DATA_WAS_SYNCHRONIZED(30L, "Your data was synchronized"),
    YOUR_DATA_WAS_NOT_ADDED(31L, "Your data was not added to your profile"),

    // pregnancy
    TEST_NEGATIVE_NOT_PREGNANT1(50L, "Your pregnancy test result is negative."),
    TEST_NEGATIVE_NOT_PREGNANT2(51L, "Your pregnancy test result is negative."),
    TEST_POSITIVE_MAY_PREGNANT(52L, "Your pregnancy test result is positive. take another test to confirm your positive result."),
    TEST_POSITIVE_MEANS_PREGNANT(53L, "Your pregnancy test result is positive which means you are pregnant."),
    TEST_POSITIVE_CONGRATULATIONS_PREGNANT(54L, "Congratulations! Your pregnancy test result is positive!"),

    // BBT REMINDER
    REMINDER_BBT(84L, "BBT reminder"),

    // rate the app
    RATE_THE_APP_ACTIVE_USERS(26L, "rate the app: Active users"),
    RATE_THE_APP_CANCELLED(27L, "rate the app: Users who canceled more than 30 days ago (still active)"),
    RATE_THE_APP_LOW_STAR(28L, "rate the app: Users who gave us 1-3 stars more than 30 days ago (still active)"),

    // 长周期
    LONGER_CYCLE_CHECK(66L, "Did you start your period?"),

    // 诊所
    CLINIC_NOTIFICATION(83L, "Clinic Notification"),

    // Period start soon
    PERIOD_STARTING_SOON_TTC(102L, "Your period is starting soon"),
    PREDICTED_PERIOD_STARTED_TTC(103L, "Did you start your period?"),
    PERIOD_STARTING_SOON_TTA(108L, "Your period is starting soon"),
    PREDICTED_PERIOD_STARTED_TTA(109L, "Did you start your period?"),
    PERIOD_STARTING_SOON_CYCLETRACKING(114L, "Your period is starting soon"),
    PREDICTED_PERIOD_STARTED_CYCLETRACKING(115L, "Did you start your period?"),

    // UPDATES
    FIRMWARE_UPDATE(118L, "New firmware update available"),
    SOFTWARE_UPDATE(119L, "New app version available"),

    // menopause
    MENOPAUSE_ONE_CYCLE_DONE(304L, ""),

    // Pay Wall
    PAY_WALL(255L, "Boost your fertility chances with Mira Premium"),

    // Iggregular cycles
    CONFIRMATION_FOR_IRREGULAR_CYCLES(291L, "We see you haven’t logged your period in [X] months.Did you miss " +
            "your period?"),

    // No Period
    NO_PERIOD(390L, "We noticed you haven’t logged a period in over 150 days"),
    // After 1 cycle in the app
    AFTER_1_CYCLE(391L, "How do you feel after your first cycle with Mira?")

    ;

    private final Long defineId;
    private final String title;

    NotificationDefineEnum(Long defineId, String title) {
        this.defineId = defineId;
        this.title = title;
    }

    public static NotificationDefineEnum get(Long defineId) {
        for (NotificationDefineEnum notificationDefineEnum : NotificationDefineEnum.values()) {
            if (notificationDefineEnum.getDefineId().equals(defineId)) {
                return notificationDefineEnum;
            }
        }
        return null;
    }
}
