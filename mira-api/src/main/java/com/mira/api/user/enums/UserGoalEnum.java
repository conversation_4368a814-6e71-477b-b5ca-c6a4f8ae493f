package com.mira.api.user.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 用户目标选项枚举
 *
 * <AUTHOR>
 */
@Getter
public enum UserGoalEnum {
    CYCLE_TRACKING(0, "Cycle tracking"),
    TTA(1, "TTA-Trying to Avoid Pregnancy"),
    TTC(2, "TTC-Trying to Conceive"),
    PREGNANCY_TRACKING(3, "Pregnancy tracking"),
    OFT(4, "Only OFT mode, Ovarian function"),

    NONE(-1, "none")
    ;

    private final Integer value;
    private final String desc;

    UserGoalEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static Integer getUserMode(Integer ttaSwitch, Integer goalStatus) {
        Integer userMode;
        if (ttaSwitch != null && ttaSwitch == 1 && Objects.equals(TTA.getValue(), goalStatus)) {
            userMode = TTA.getValue();
        } else if (Objects.equals(OFT.getValue(), goalStatus)) {
            userMode = OFT.getValue();
        } else if (Objects.equals(CYCLE_TRACKING.getValue(), goalStatus)) {
            userMode = CYCLE_TRACKING.getValue();
        } else if (Objects.equals(PREGNANCY_TRACKING.getValue(), goalStatus)) {
            userMode = PREGNANCY_TRACKING.getValue();
        } else {
            userMode = TTC.getValue();
        }
        return userMode;
    }

    public static UserGoalEnum get(Integer value) {
        for (UserGoalEnum userGoalEnum : values()) {
            if (userGoalEnum.getValue().equals(value)) {
                return userGoalEnum;
            }
        }
        return null;
    }
}
