package com.mira.api.bluetooth.util;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.TestingProductDayDTO;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.enums.OvulationStatusEnum;
import com.mira.api.bluetooth.enums.OvulationTypeEnum;
import com.mira.api.user.enums.PhaseEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.LocalDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 周期信息工具类
 *
 * <AUTHOR>
 */
@Slf4j
public final class CycleDataUtil {
    private CycleDataUtil() {
    }

    /**
     * 获取指定日期处于周期的哪个阶段
     *
     * @param day   日期
     * @param cycle 周期数据
     * @return PhaseEnum
     */
    public static PhaseEnum getPhase(String day, CycleDataDTO cycle) {
        Predicate<CycleDataDTO> fertilePhase = cycleData
                -> StringUtils.isNotEmpty(cycleData.getDate_FW_start()) && StringUtils.isNotEmpty(cycleData.getDate_FW_end())
                && LocalDateUtil.isBetweenDateAndEqualLeft(day, cycleData.getDate_FW_start(), cycleData.getDate_FW_end());
        Predicate<CycleDataDTO> lutealPhase = cycleData
                -> StringUtils.isNotEmpty(cycleData.getDate_ovulation())
                && LocalDateUtil.minusToDay(day, cycleData.getDate_ovulation()) > 0;

        if (fertilePhase.test(cycle)) {
            return PhaseEnum.FERTILE_WINDOW;
        }
        if (lutealPhase.test(cycle)) {
            return PhaseEnum.LUTEAL_PHASE;
        }
        return PhaseEnum.FOLLICULAR_PHASE;
    }

    /**
     * 是否处于经期
     *
     * @param day   日期
     * @param cycle 周期数据
     * @return true/false
     */
    public static boolean inPeriod(String day, CycleDataDTO cycle) {
        String periodStart = cycle.getDate_period_start();
        String periodEnd = cycle.getDate_period_end();
        if (StringUtils.isAnyBlank(periodStart, periodEnd)) {
            return false;
        }
        return LocalDateUtil.isBetweenDateAndEqualLeft(day, periodStart, periodEnd);
    }

    /**
     * 是否处于排卵期
     *
     * @param day   日期
     * @param cycle 周期数据
     * @return true/false
     */
    public static boolean inFertileWindow(String day, CycleDataDTO cycle) {
        String fwStart = cycle.getDate_FW_start();
        String fwEnd = cycle.getDate_FW_end();
        if (StringUtils.isAnyBlank(fwStart, fwEnd)) {
            return false;
        }
        return LocalDateUtil.isBetweenDateAndEqualLeft(day, fwStart, fwEnd);
    }

    /**
     * 返回当天的ovulation类型
     *
     * @param day   日期
     * @param cycle 周期数据
     * @return OvulationTypeEnum
     */
    public static OvulationTypeEnum getOvulationType(String day, CycleDataDTO cycle) {
        String dateOvulation = cycle.getDate_ovulation();
        Integer ovulationType = cycle.getOvulation_type();

        int minusToDay = LocalDateUtil.minusToDay(day, dateOvulation);
        if (minusToDay != 0) {
            return null;
        }

        return OvulationTypeEnum.get(ovulationType);
    }

    /**
     * 返回ovulation状态
     *
     * @param day   日期
     * @param cycle 周期数据
     * @return OvulationStatusEnum
     */
    @Deprecated
    public static OvulationStatusEnum getOvulationStatus(String day, CycleDataDTO cycle) {
        Integer ovulationType = cycle.getOvulation_type();
        if (Objects.equals(OvulationTypeEnum.PREDICTED.getCode(), ovulationType)) {
            if (LocalDateUtil.minusToDay(day, cycle.getDate_ovulation()) == 0) {
                return OvulationStatusEnum.PREDICTED;
            }
        }
        if (Objects.equals(OvulationTypeEnum.DETECTED.getCode(), ovulationType)
                || Objects.equals(OvulationTypeEnum.DETECTED_CONFIRMED.getCode(), ovulationType)) {
            if (LocalDateUtil.minusToDay(day, cycle.getDate_LH_surge()) == 0) {
                return OvulationStatusEnum.DETECTED;
            }
        }
        if (Objects.equals(OvulationTypeEnum.PREDICTED_CONFIRMED.getCode(), ovulationType)
                || Objects.equals(OvulationTypeEnum.DETECTED_CONFIRMED.getCode(), ovulationType)) {
            List<String> datePdgRise = cycle.getDate_PDG_rise();
            if (CollectionUtils.isNotEmpty(datePdgRise)) {
                for (String date : datePdgRise) {
                    if (LocalDateUtil.minusToDay(day, date) == 0) {
                        return OvulationStatusEnum.CONFIRMED;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取当前实周期的索引
     *
     * @param cycleDataDTOS 周期数据
     * @return int
     */
    public static int getRealCurrentCycleDataIndex(List<CycleDataDTO> cycleDataDTOS) {
        int size = cycleDataDTOS.size();
        for (int i = size - 1; i > -1; i--) {
            CycleDataDTO cycleDataDTO = cycleDataDTOS.get(i);
            if (cycleDataDTO.getCycle_status() == CycleStatusEnum.REAL_CYCLE.getStatus()) {
                return i;
            }
        }
        return size - 1;
    }

    /**
     * 取包含当天日期在内的周期信息
     *
     * @param cycleDataList 周期数据
     * @param size          list size
     * @param userLocalTime 用户当地时间
     * @return CycleDataDTO
     */
    public static CycleDataDTO getCurentDayCycleData(List<CycleDataDTO> cycleDataList, int size, String userLocalTime) {
        if (size == 0) {
            return null;
        }

        CycleDataDTO latestCycle = cycleDataList.get(size - 1);
        int minusDay = LocalDateUtil.minusToDay(userLocalTime, latestCycle.getDate_period_start());

        if (minusDay >= latestCycle.getLen_cycle()) {
            return null;
        }

        if (minusDay >= 0) {
            return latestCycle;
        }

        return getCurentDayCycleData(cycleDataList, --size, userLocalTime);
    }

    /**
     * 获取某天所在的周期信息
     *
     * @param date          当天日期
     * @param cycleDataDTOS 周期数据
     * @return CycleDataDTO
     */
    public static CycleDataDTO getCurrentCycleData(String date, List<CycleDataDTO> cycleDataDTOS) {
        int size = cycleDataDTOS.size();
        for (int i = size - 1; i > -1; i--) {
            CycleDataDTO cycleDataDTO = cycleDataDTOS.get(i);
            if (LocalDateUtil.minusToDay(date, cycleDataDTO.getDate_period_start()) >= 0) {
                return cycleDataDTOS.get(i);
            }
        }
        return cycleDataDTOS.get(size - 1);
    }

    /**
     * 获取对应的日期是CD几
     *
     * @param nowDate      当天日期
     * @param cycleDataDTO 周期数据
     * @return CD
     */
    public static int getCDByCycle(String nowDate, CycleDataDTO cycleDataDTO) {
        String datePeriodStart = cycleDataDTO.getDate_period_start();
        if (StringUtils.isBlank(datePeriodStart)) {
            return -1;
        }
        List<String> cycleCdIndex = cycleDataDTO.getCycle_cd_index();
        if (CollectionUtils.isEmpty(cycleCdIndex)) {
            return -1;
        }
        int cdIndex = LocalDateUtil.minusToDay(nowDate, datePeriodStart);
        if (cdIndex <= -1 || cdIndex >= cycleCdIndex.size()) {
            return -1;
        }

        return cdIndex;
    }

    /**
     * 取当前周期的hormone测试数据
     *
     * @param currentCycle 当前周期
     * @param hormoneDTOS  所有hormone测试数据
     * @return List<HormoneDTO>
     */
    public static List<HormoneDTO> hormoneByCurrentCycle(CycleDataDTO currentCycle, List<HormoneDTO> hormoneDTOS) {
        List<HormoneDTO> result = new ArrayList<>();

        String periodStartDate = currentCycle.getDate_period_start() + " 00:00:00";
        for (int i = hormoneDTOS.size() - 1; i >= 0; i--) {
            HormoneDTO hormoneDTO = hormoneDTOS.get(i);
            try {
                if (LocalDateUtil.after(hormoneDTO.getTest_time(), periodStartDate, DatePatternConst.DATE_TIME_PATTERN)) {
                    result.add(hormoneDTO);
                }
            } catch (Exception e) {
                //
            }
        }

        return result;
    }

    /**
     * 取特定周期的hormone测试数据
     *
     * @param cycleDataDTO 指定周期
     * @param hormoneDTOS  所有hormone测试数据
     * @return List<HormoneDTO>
     */
    public static List<HormoneDTO> hormoneBySpecialCycle(CycleDataDTO cycleDataDTO, List<HormoneDTO> hormoneDTOS) {
        String startDate = cycleDataDTO.getDate_period_start();
        String endDate = LocalDateUtil.plusDay(startDate,
                cycleDataDTO.getLen_cycle() - 1, DatePatternConst.DATE_PATTERN);

        return hormoneDTOS.stream()
                .filter(hormoneDTO
                        -> LocalDateUtil.isBetweenDateAndEqual(hormoneDTO.getTest_time(), startDate, endDate))
                .collect(Collectors.toList());
    }

    /**
     * 对应日期内测试了几种试剂
     *
     * @param date        日期
     * @param hormoneDTOS 测试数据
     * @return int
     */
    public static int wandCountByHormone(String date, List<HormoneDTO> hormoneDTOS) {
        List<HormoneDTO> result = hormoneDTOS.stream()
                .filter(hormoneDTO -> date.equals(hormoneDTO.getTest_time().substring(0, 10)))
                .collect(Collectors.toList());
        Set<Integer> wandCountSet = new HashSet<>();
        for (HormoneDTO hormoneDTO : result) {
            wandCountSet.add(hormoneDTO.getTest_results().getWand_type());
        }
        return wandCountSet.size();
    }

    /**
     * 测试占比
     *
     * @param currentCycle    当前周期
     * @param currentHormones 当前周期的测试数据
     * @return 百分比
     */
    public static long testRatioByCurrentCycle(CycleDataDTO currentCycle, List<HormoneDTO> currentHormones) {
        // 推荐的测试日（不区分试剂类型）
        Set<String> testDaySet = getTestDaySet(currentCycle);

        // 推荐测试日数
        long recommendDayCount = testDaySet.size();
        if (recommendDayCount == 0) {
            return -1;
        }
        // 实际测试日数
        long realTestDayCount = 0;

        for (HormoneDTO hormoneDTO : currentHormones) {
            String testDate = hormoneDTO.getTest_time().substring(0, 10);
            if (testDaySet.contains(testDate)) {
                realTestDayCount++;
            }
        }

        return Math.round((realTestDayCount * 100.0) / recommendDayCount);
    }

    /**
     * 跳过推荐测试的天数
     *
     * @param currentCycle    当前周期
     * @param currentHormones 当前周期的测试数据
     * @param today           当天
     * @return 天数
     */
    public static long skipTestCountByCurrentCycle(CycleDataDTO currentCycle, List<HormoneDTO> currentHormones, String today) {
        // 推荐的测试日（不区分试剂类型）
        Set<String> testDaySet = getTestDaySet(currentCycle);
        // 以当天为终点
        testDaySet.removeIf(testDay -> testDay.compareTo(today) >= 0);

        // 推荐测试日数
        long recommendDayCount = testDaySet.size();
        if (recommendDayCount == 0) {
            return -1;
        }
        // 实际测试日数
        long realTestDayCount = 0;

        for (HormoneDTO hormoneDTO : currentHormones) {
            String testDate = hormoneDTO.getTest_time().substring(0, 10);
            if (testDaySet.contains(testDate)) {
                realTestDayCount++;
            }
        }

        return recommendDayCount - realTestDayCount;
    }

    /**
     * 获取测试日列表
     *
     * @param currentCycle 周期
     * @return Set
     */
    public static Set<String> getTestDaySet(CycleDataDTO currentCycle) {
        TestingProductDayDTO testingDayList = currentCycle.getTesting_day_list();
        if (testingDayList == null) {
            return new HashSet<>();
        }
        Set<String> testDaySet = new HashSet<>();
        List<String> product02 = testingDayList.getProduct02();
        if (CollectionUtils.isNotEmpty(product02)) {
            testDaySet.addAll(product02);
        }
        List<String> product03 = testingDayList.getProduct03();
        if (CollectionUtils.isNotEmpty(product03)) {
            testDaySet.addAll(product03);
        }
        List<String> product09 = testingDayList.getProduct09();
        if (CollectionUtils.isNotEmpty(product09)) {
            testDaySet.addAll(product09);
        }
        List<String> product12 = testingDayList.getProduct12();
        if (CollectionUtils.isNotEmpty(product12)) {
            testDaySet.addAll(product12);
        }
        List<String> product14 = testingDayList.getProduct14();
        if (CollectionUtils.isNotEmpty(product14)) {
            testDaySet.addAll(product14);
        }
        List<String> product16 = testingDayList.getProduct16();
        if (CollectionUtils.isNotEmpty(product16)) {
            testDaySet.addAll(product16);
        }
        return testDaySet;
    }

    /**
     * 获取当天测试试剂类型列表
     *
     * @param currentCycle 周期
     * @return Set
     */
    public static Set<Integer> getTestWandSet(String today, CycleDataDTO currentCycle) {
        Set<Integer> testWandSet = new HashSet<>();
        TestingProductDayDTO testingDayList = currentCycle.getTesting_day_list();
        if (testingDayList == null) {
            return testWandSet;
        }
        if (CollectionUtils.isNotEmpty(testingDayList.getProduct02()) && testingDayList.getProduct02().contains(today)) {
            testWandSet.add(WandTypeEnum.HCG.getInteger());
        }
        if (CollectionUtils.isNotEmpty(testingDayList.getProduct03()) && testingDayList.getProduct03().contains(today)) {
            testWandSet.add(WandTypeEnum.E3G_LH.getInteger());
        }
        if (CollectionUtils.isNotEmpty(testingDayList.getProduct09()) && testingDayList.getProduct09().contains(today)) {
            testWandSet.add(WandTypeEnum.PDG.getInteger());
        }
        if (CollectionUtils.isNotEmpty(testingDayList.getProduct12()) && testingDayList.getProduct12().contains(today)) {
            testWandSet.add(WandTypeEnum.LH_E3G_PDG.getInteger());
        }
        if (CollectionUtils.isNotEmpty(testingDayList.getProduct14()) && testingDayList.getProduct14().contains(today)) {
            testWandSet.add(WandTypeEnum.HCG_QUALITATIVE.getInteger());
        }
        if (CollectionUtils.isNotEmpty(testingDayList.getProduct16()) && testingDayList.getProduct16().contains(today)) {
            testWandSet.add(WandTypeEnum.FSH.getInteger());
        }
        return testWandSet;
    }

    /**
     * 获取经期列表
     *
     * @param cycleDataDTOS 周期数据
     * @return List
     */
    public static List<String> getPeriodList(List<CycleDataDTO> cycleDataDTOS, CycleStatusEnum cycleStatusEnum) {
        List<String> predictedPeriodList = new ArrayList<>();

        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            if (cycleStatusEnum.getStatus() != cycleDataDTO.getCycle_status()) {
                continue;
            }
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            String datePeriodEnd = cycleDataDTO.getDate_period_end();
            predictedPeriodList.add(datePeriodStart);
            int minus = LocalDateUtil.minusToDay(datePeriodEnd, datePeriodStart) - 1;
            for (int i = 1; i <= minus; i++) {
                predictedPeriodList.add(LocalDateUtil.plusDay(datePeriodStart, i, DatePatternConst.DATE_PATTERN));
            }
        }

        return predictedPeriodList;
    }

    /**
     * 获取所有确定排卵的日期
     *
     * @param cycleDataDTOS 周期数据
     * @return List
     */
    public static List<String> getAllConfirmOvuDayList(List<CycleDataDTO> cycleDataDTOS) {
        List<String> confirmOvuDayList = new ArrayList<>();
        for (CycleDataDTO cycleData : cycleDataDTOS) {
            Integer ovulationType = cycleData.getOvulation_type();
            if (Objects.equals(OvulationTypeEnum.PREDICTED_CONFIRMED.getCode(), ovulationType)
                    || Objects.equals(OvulationTypeEnum.DETECTED_CONFIRMED.getCode(), ovulationType)) {
                List<String> datePdgRise = cycleData.getDate_PDG_rise();
                if (CollectionUtils.isNotEmpty(datePdgRise)) {
                    confirmOvuDayList.add(cycleData.getDate_ovulation());
                }
            }
        }

        return confirmOvuDayList;
    }

    /**
     * 当天是否完成了所有推荐的测试
     *
     * @param today       今天
     * @param hormoneDTOS 荷尔蒙数据
     * @param testWandSet 当天需要测试的试剂类型
     * @return boolean
     */
    public static boolean todayCompleteAllTest(String today, List<HormoneDTO> hormoneDTOS, Set<Integer> testWandSet) {
        return hormoneDTOS.stream()
                .filter(hormoneDTO -> LocalDateUtil.minusToDay(today, hormoneDTO.getTest_time()) == 0)
                .map(hormoneDTO -> hormoneDTO.getTest_results().getWand_type())
                .collect(Collectors.toSet())
                .containsAll(testWandSet);
    }
}
