package com.mira.api.bluetooth.enums;

import lombok.Getter;

/**
 * 算法请求类型
 *
 * <AUTHOR>
 */
@Getter
public enum AlgorithmRequestTypeEnum {
    NEW_HORMONE_DATA(1, "新数据上传"),
    EDIT_PERIOD(20, "编辑经期"),
    EXTEND_PERIOD(21, "延长经期"),
    CONFIRM_PERIOD(22, "确认经期"),
    REGISTER(23, "注册用户时编辑经期"),
    EDIT_PERIOD_LENGTH(24, "修改用户平均经期长度"),
    EDIT_CYCLE_LENGTH(25, "修改用户平均周期长度"),
    TRANSFER(26, "v3到v4的迁移数据"),
    SYSTEM_EDIT(27, "系统帮用户编辑经期"),
    EDIT_GOAL(28, "修改人生目标TTA/TTC"),
    EDIT_CONDITIONS(29, "修改conditions"),
    CHANGE_THRESHOLD_MODE(30, "修改threshold mode"),
    GET_TIPS(31, "获取算法推荐的tips"),
    CONSUMER_MAX_FIRST(32, "MAX试纸首次上传之后编辑经期"),
    LONGER_PERIOD_EDIT(33, "用户经期没有来，说明当前是长周期"),
    MIRA_REPORT(34, "report"),
    CHANGE_SCHEDULE(35, "修改schedule"),
    CHANGE_PREGNANT_MODE_INFO(36, "修改怀孕模式信息"),
    REMOVE_PREGNANT_MODE_INFO(37, "结束怀孕模式"),
    EDIT_NO_PERIOD(38, "编辑无经期"),

    GET_MENOPAUSE_RESULT_testingPlan(4001, "获取更年期算法返回结果"),
    GET_MENOPAUSE_RESULT_testingPlanDetail(4002, "获取更年期算法返回结果"),
    GET_MENOPAUSE_RESULT_testingPlanForClient(4003, "获取更年期算法返回结果"),
    GET_MENOPAUSE_RESULT_dataRangeForClient(4004, "获取更年期算法返回结果"),
    GET_MENOPAUSE_RESULT_buildCycleAnalysisVO(4005, "获取更年期算法返回结果"),
    GET_MENOPAUSE_RESULT_v5homeCycleData(4006, "获取更年期算法返回结果"),
    GET_MENOPAUSE_RESULT_report(4007, "获取更年期算法返回结果"),
    GET_MENOPAUSE_RESULT_clinicPatientCycleAnalysis(4007, "获取更年期算法返回结果"),

    CHANGE_MENOPAUSE_FLAG(41, "修改更年期标识"),
    EDIT_MANUAL_OVULATION(42, "用户编辑ovulation"),
    SET_WAND_COUNT(43, "设置用户拥有哪些试剂"),
    ADD_HORMONE_DATA(46, "用户添加手动数据")
    ;

    private final Integer value;
    private final String description;

    AlgorithmRequestTypeEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public static AlgorithmRequestTypeEnum get(Integer value) {
        for (AlgorithmRequestTypeEnum algorithmRequestTypeEnum : AlgorithmRequestTypeEnum.values()) {
            if (algorithmRequestTypeEnum.getValue().equals(value)) {
                return algorithmRequestTypeEnum;
            }
        }
        return null;
    }
}
