package com.mira.third.party.enums;

import com.mira.core.response.enums.ICodeEnum;

/**
 * 三方服务响应编码枚举
 *
 * <AUTHOR>
 */
public enum FeignCodeEnum implements ICodeEnum {
    SERVER_ERROR(5000, "Oops, our servers had a hiccup. Please try again."),
    BLOG_ERROR(5100, "We hit a snag with the blog content. How about checking other news?")
    ;

    private final int code;
    private final String msg;

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    FeignCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
